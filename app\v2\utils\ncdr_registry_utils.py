from lxml import etree
import xml.etree.ElementTree as ET
import base64
import os
import shutil
import pyzipper
from Cryptodome.Cipher import AES
import hashlib
import datetime
import copy
from v2.constants import FORM_TABLE_IDS, FORM_EVENTS
from config import settings
import pandas as pd
import logging
import os
import time

# Set up a dedicated logger for medication processing
med_logger = logging.getLogger('medication_processing')
med_logger.setLevel(logging.DEBUG)

# Create a file handler for the medication logger
log_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'logs')
os.makedirs(log_dir, exist_ok=True)
med_log_file = os.path.join(log_dir, f'medication_processing_{int(time.time())}.log')
file_handler = logging.FileHandler(med_log_file)
file_handler.setLevel(logging.DEBUG)

# Create a formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add the handler to the logger
med_logger.addHandler(file_handler)
med_logger.info(f"Medication processing log started, writing to {med_log_file}")

client_id = settings.NCDR_CLIENT_ID

table_ids = FORM_TABLE_IDS
events = FORM_EVENTS

def get_element_info(xml_file, reference_value):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    for element in root.findall(".//element"):
        if element.get("reference") == reference_value:
            code = element.get("code")
            name = element.find("name").text if element.find("name") is not None else "N/A"
            return {"code": code, "name": name}
    return None

def update_xml_value(xml_file, code, display_name, new_value):
    tree = ET.parse(xml_file)
    root = tree.getroot()
    if "xmlns:xsd" not in root.attrib:
        root.set("xmlns:xsd", "http://www.w3.org/2001/XMLSchema")

    for element in root.findall(".//element"):
        if element.get("code") == code and element.get("displayName") == display_name:
            value_element = element.find("value")
            if value_element is not None:
                xsi_type = value_element.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type")
                if xsi_type == "CD":
                    value_element.attrib.pop("value", None)
                    continue
                if xsi_type == "BL":
                    print(new_value, "new_value")
                    if str(new_value).lower() in ["yes", "true"]:
                        new_value = "true"
                    elif str(new_value).lower() in ["no", "false"]:
                        new_value = "false"
                    # else:
                    #     new_value = None
                elif xsi_type == "DT":
                    try:
                        from datetime import datetime
                        date_obj = datetime.strptime(str(new_value), "%Y-%m-%d")
                        new_value = date_obj.strftime("%Y-%m-%d")
                    except ValueError:
                        new_value = None
                if new_value is None or str(new_value).strip() == "":
                    value_element.attrib.pop("value", None)
                else:
                    value_element.set("value", new_value)

    # for value_element in root.iter("value"):
    #     xsi_type = value_element.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type")
    #     val = value_element.attrib.get("value")

    #     if val is None or val.strip() == "" or xsi_type == "CD":
    #         value_element.attrib.pop("value", None)

    tree.write(xml_file, encoding='utf-8', xml_declaration=True)
    return xml_file


def get_field_ids_and_values(data: dict, case_id):
    field_data = []
    updated_xml_path = None

    xml_folder = os.path.abspath(rf"v2/data_sources/xml/{case_id}")
    os.makedirs(xml_folder,exist_ok = True)
    original_xml_path = os.path.abspath("v2/data_sources/ncdr/generated_registry_document.xml")
    copied_xml_path = os.path.join(xml_folder, f"LAAO{client_id}-2025Q1.xml")

    shutil.copy(original_xml_path, copied_xml_path)

    def extract_field_data(obj):
        nonlocal updated_xml_path
        if isinstance(obj, dict):
            if "field_id" in obj and "value" in obj:
                field_data.append({"field_id": obj["field_id"], "value": obj["value"], "options": obj.get("options",None), "input_type": obj.get("input_type")})
                if field_data:
                    dict1 = get_element_info(os.path.abspath("v2/data_sources/ncdr/dict.xml"), obj["field_id"])
                    if dict1:
                        updated_xml_path = update_xml_value(copied_xml_path, dict1["code"], dict1["name"], obj["value"])
            for key in obj:
                extract_field_data(obj[key])
        elif isinstance(obj, list):
            for item in obj:
                extract_field_data(item)
    extract_field_data(data)
    if updated_xml_path is None:
        logging.info("ERROR: No XML updates were made")
        raise ValueError("Failed to update XML file")
    logging.info(f"updated xml path : {updated_xml_path}")

    return updated_xml_path

def validate_xml(xml_input):
    """
    Validate XML against XSD schema.
    
    Args:
        xml_input: Can be either a file path (str) or an ElementTree object
        
    Returns:
        List of error messages, empty if validation succeeds
    """
    errors = []
    xsd_file = os.path.abspath("v2/data_sources/ncdr/RTD.xsd")
    
    # Load XSD schema
    with open(xsd_file, 'rb') as f:
        xsd_doc = etree.parse(f)
        xsd_schema = etree.XMLSchema(xsd_doc)
    
    # Handle different input types
    if isinstance(xml_input, str):
        # Input is a file path
        with open(xml_input, 'rb') as f:
            xml_doc = etree.parse(f)
    elif isinstance(xml_input, ET.ElementTree):
        # Input is an ElementTree object
        # Convert ElementTree to lxml.etree for validation
        xml_string = ET.tostring(xml_input.getroot(), encoding='utf-8')
        xml_doc = etree.fromstring(xml_string)
    else:
        raise TypeError("xml_input must be either a file path or an ElementTree object")
    
    # Validate XML
    if xsd_schema.validate(xml_doc):
        logging.info("✅ XML is valid against the XSD.")
        return errors
    else:
        logging.info("❌ XML validation failed.")
        for error in xsd_schema.error_log:
            logging.info(f"Line {error.line}: {error.message}")
            errors.append(f"Line {error.line}: {error.message}")
        return errors

def compute_hash(plain_text: str, hash_algorithm: str = None, salt_bytes: bytes = None) -> str:
    if salt_bytes is None:
        min_salt_size = 4
        max_salt_size = 8
        salt_size = secrets.randbelow(max_salt_size - min_salt_size + 1) + min_salt_size
        salt_bytes = secrets.token_bytes(salt_size)
    plain_text_bytes = plain_text.encode('utf-8')
    plain_text_with_salt_bytes = plain_text_bytes + salt_bytes
    hash_algorithm = (hash_algorithm or '').upper()
    hash_algorithms = {
        'SHA1': hashlib.sha1,
        'SHA256': hashlib.sha256,
        'SHA384': hashlib.sha384,
        'SHA512': hashlib.sha512,
        'MD5': hashlib.md5
    }
    hash_func = hash_algorithms.get(hash_algorithm, hashlib.md5)
    hash_bytes = hash_func(plain_text_with_salt_bytes).digest()
    hash_with_salt_bytes = hash_bytes + salt_bytes
    hash_value = base64.b64encode(hash_with_salt_bytes).decode('utf-8')

    return hash_value

def zip_and_encrypt_file(file_path,case_id, secrets):
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File '{file_path}' not found.")
    zip_folder = os.path.abspath(f"v2/data_sources/xml/{case_id}")
    os.makedirs(zip_folder,exist_ok = True)
    zip_file_name = f"LAAO{client_id}-2025Q1.zip"
    zip_file_path = os.path.join(zip_folder, zip_file_name)

    with pyzipper.AESZipFile(zip_file_path, 'w', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zipf:
        zipf.setpassword(secrets.encode())
        zipf.write(file_path, os.path.basename(file_path))
    logging.info(f"Encrypted zip file created: {zip_file_path}")

    return zip_file_path

def unzip_and_decrypt_file(zip_file, secrets):
    with pyzipper.AESZipFile(zip_file, 'r', compression=pyzipper.ZIP_DEFLATED, encryption=pyzipper.WZ_AES) as zipf:
        zipf.setpassword(secrets.encode())
        file_name = zipf.namelist()[0]  # Assuming only one file
        logging.info(f"Extracting file: {file_name}" )
        with zipf.open(file_name) as extracted_file:
            extracted_text = extracted_file.read().decode("utf-8")

    return extracted_text


def construct_ncdr_xml(data_dict: dict, case_id: str) -> str:
    xml_folder = os.path.abspath(rf"v2/data_sources/xml/{case_id}")
    os.makedirs(xml_folder,exist_ok = True)
    year = datetime.datetime.now().year
    op_file_path = f"{xml_folder}/LAAO{client_id}-{year}Q1.xml"
    processed_data = extract_fields_with_label(data_dict)
    process_ncdr_data(processed_data, op_file_path, original_data=data_dict)

    return op_file_path


def extract_fields_with_label(data_dict, table_ids=None):
    """
    Traverse through a dictionary and extract fields with a 'label' key.
    Also handles nested conditional fields in if_yes, if_hemorrhage, if_alive, if_deceased.

    Args:
        data_dict (dict): The input dictionary to traverse
        table_ids (list): List of field IDs that should be treated as table fields

    Returns:
        list: A list of dictionaries containing field_id, value, options, and input_type
    """
    if table_ids is None:
        table_ids = []

    result = []

    def traverse(obj, path=None, parent_value=None):
        if path is None:
            path = []

        if isinstance(obj, dict):
            # Check if this is a field with a label
            if 'label' in obj:
                if obj.get('field_id', None) not in table_ids:
                    field_info = {
                        'field_id': obj.get('field_id', None),
                        'label': obj.get('label', None),
                        'value': obj.get('value', None),
                        'options': obj.get('options', None),
                        'input_type': obj.get('input_type', None)
                    }
                    result.append(field_info)

                    # Handle nested conditional fields
                    current_value = obj.get('value', None)

                    # Process if_yes when value is "Yes" or True
                    if current_value in ["Yes", "yes", True, "true"] and 'if_yes' in obj:
                        traverse(obj['if_yes'], path + ['if_yes'], current_value)

                    # Process if_hemorrhage ONLY when current field indicates hemorrhage
                    if current_value in ["Yes", "yes", True, "true"] and 'if_hemorrhage' in obj:
                        traverse(obj['if_hemorrhage'], path + ['if_hemorrhage'], current_value)

                    # Process if_alive when value is "Alive"
                    if current_value in ["Alive", "alive"] and 'if_alive' in obj:
                        traverse(obj['if_alive'], path + ['if_alive'], current_value)

                    # Process if_deceased when value is "Deceased"
                    if current_value in ["Deceased", "deceased"] and 'if_deceased' in obj:
                        traverse(obj['if_deceased'], path + ['if_deceased'], current_value)

            # Continue traversing all key-value pairs
            for key, value in obj.items():
                if key == "field_id" and value in table_ids:
                    result.append(obj)
                    break

                # Special handling for intra/post-procedure events nested structure
                if key == 'events' and isinstance(value, dict) and 'elements' in value:
                    # This is the intra/post-procedure events structure
                    # Always traverse the events.elements structure to find individual event fields
                    traverse(value, path + [key], parent_value)
                elif key == 'elements' and isinstance(value, dict):
                    # This is the elements container - traverse all categories
                    for category_key, category_value in value.items():
                        if isinstance(category_value, dict):
                            # Traverse each category (cardiovascular, etc.)
                            traverse(category_value, path + [key, category_key], parent_value)
                # Skip conditional blocks that are already processed above
                elif key not in ['if_yes', 'if_hemorrhage', 'if_alive', 'if_deceased', 'events', 'elements']:
                    new_path = path + [key]
                    traverse(value, new_path, parent_value)

        elif isinstance(obj, list):
            for item in obj:
                traverse(item, path, parent_value)

    traverse(data_dict)
    return result

def remove_tag(xml_tree: ET.ElementTree, tag_name: str):

    for parent in xml_tree.getroot():
        for element in parent.findall(f".//{tag_name}"):
            # Skip removal if the element has the preserve attribute set to "true"
            if element.get("preserve") != "true":
                parent.remove(element)

def remove_empty_tags(xml_tree: ET.ElementTree, tag_name: str):
    """
    Removes all instances of the specified tag from the XML tree if they have no child elements,
    regardless of their depth in the tree.

    Parameters:
    xml_tree (ElementTree): The XML tree to modify
    tag_name (str): The name of the tag to remove if empty

    Returns:
    ElementTree: The modified XML tree
    """
    root = xml_tree.getroot()

    _remove_empty_tags_recursive(root, tag_name)

    return xml_tree

def _remove_empty_tags_recursive(element, tag_name):
    """
    Recursively process elements to find and remove empty tags.
    """
    for child in list(element):
        _remove_empty_tags_recursive(child, tag_name)

    for child in list(element):
        # Skip removal if the element has the preserve attribute set to "true"
        if child.tag == tag_name and len(list(child)) == 0 and child.get("preserve") != "true":
            element.remove(child)


def update_xml_element_by_attributes(tree: ET.ElementTree | ET.Element, attribute_conditions: dict, section_condition: dict, new_value, unit=None):
    """
    Updates XML <element> tags based on attribute match and sets the <value> tag's 'value' attribute.

    Parameters:
        tree (ElementTree): The parsed XML tree.
        attribute_conditions (dict): Attribute conditions to match in <element> tags.
        new_value (str): Value to set in the <value> child element.
        unit (str, optional): Unit to set in the <value> child element for PQ data types.
    """
    sections = get_all_section(tree, section_condition) or [tree]
    for section in sections:
        for element in section.findall(".//element"):
            if all(element.get(attr) == val for attr, val in attribute_conditions.items()):
                value_tags = element.findall("value")
                for v in value_tags:
                    # Handle BL type values specially
                    if v.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL":
                        if new_value is not None and str(new_value).strip():
                            bl_value = str(new_value).lower()
                            v.set("value", "true" if bl_value in ("yes", "true") else "false")
                        else:
                            v.set("value", "false")
                    else:
                        v.set("value", str(new_value))

                    # Add unit attribute if provided and data type is PQ
                    if unit and v.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "PQ":
                        v.set("unit", str(unit))
                        logging.info(f"Updated element with {attribute_conditions} to value: {new_value}, unit: {unit}")
                    else:
                        logging.info(f"Updated element with {attribute_conditions} to value: {new_value}")



def filter_element_values_by_code(xml_tree: ET.ElementTree | ET.Element, condition: dict, section_condition: dict, allowed_display_names: list[str]):
    """
    Keeps only <value> tags whose displayName is in the allowed list,
    under an <element> tag that matches the given condition.

    Parameters:
        xml_tree (ElementTree): The parsed XML tree.
        condition (dict): Attribute conditions to match on <element> tag (e.g. {"code": "123", "displayName": "Race"}).
        allowed_display_names (list of str): List of displayName values to keep in <value> tags.
    """
    sections = get_all_section(xml_tree, section_condition) or [xml_tree]
    allowed_set = {name.strip().lower() for name in allowed_display_names}
    
    logging.info(f"FILTER_ELEMENT: Starting with allowed_display_names: {allowed_display_names}")
    logging.info(f"FILTER_ELEMENT: Converted to allowed_set: {allowed_set}")
    logging.info(f"FILTER_ELEMENT: Condition: {condition}, Section condition: {section_condition}")
    
    # Log the number of sections found
    logging.info(f"FILTER_ELEMENT: Found {len(sections)} matching sections")

    # Special handling for medication sections
    is_medication_section = False
    section_code = None
    if section_condition and section_condition.get("code") in ["PREPROCMED", "DCMEDS", "ADJMEDS"]:
        is_medication_section = True
        section_code = section_condition.get("code")
        logging.info(f"FILTER_ELEMENT: Special handling for medication section: {section_code}")

    for section in sections:
        logging.info(f"FILTER_ELEMENT: Processing section: {section.get('code')}, {section.get('displayName')}")
        elements = section.findall(".//element")
        logging.info(f"FILTER_ELEMENT: Found {len(elements)} elements in this section")
        
        for element in elements:
            element_attrs = {attr: element.get(attr) for attr in ['code', 'displayName', 'codeSystem']}
            logging.info(f"FILTER_ELEMENT: Checking element: {element_attrs}")
            logging.info(f"FILTER_ELEMENT: Comparing with condition: {condition}")
            
            if all(element.get(attr) == val for attr, val in condition.items()):
                logging.info(f"FILTER_ELEMENT: ✅ Element matches condition")
                value_tags = element.findall("value")
                logging.info(f"FILTER_ELEMENT: Found {len(value_tags)} value tags")
                
                # For medication sections, we need special handling
                if is_medication_section:
                    # Check if this is a medication status element
                    is_status_element = (section_code == "PREPROCMED" and element.get("displayName") == "Pre-Procedure Medication Administered") or \
                                       (section_code == "DCMEDS" and element.get("displayName") == "Current Medications at Time of Event") or \
                                       (section_code == "ADJMEDS" and element.get("displayName") == "Current Medications at Time of Event")
                    
                    if is_status_element:
                        logging.info(f"Filtering medication status element: {element.get('displayName')} with allowed codes: {allowed_display_names}")
                        # Keep only the value tags that match the allowed codes
                        to_remove = []
                        for value_tag in value_tags:
                            value_code = value_tag.get("code", "").strip().lower()
                            value_display = value_tag.get("displayName", "").strip().lower()
                            logging.info(f"Checking value tag: code={value_code}, displayName={value_display}")
                            
                            # Check if this value matches any of our allowed codes
                            logging.info(f"Comparing value code '{value_code}' with allowed codes: {[code.strip().lower() for code in allowed_display_names]}")
                            match_found = False
                            for allowed_code in allowed_display_names:
                                allowed_code_lower = allowed_code.strip().lower()
                                logging.info(f"Comparing with allowed code: '{allowed_code_lower}'")
                                if allowed_code_lower == value_code:
                                    match_found = True
                                    logging.info(f"MATCH FOUND: '{value_code}' matches '{allowed_code_lower}'")
                                    break
                            
                            if not match_found:
                                to_remove.append(value_tag)
                                logging.info(f"Marked for removal (not in allowed list): <value code='{value_code}' displayName='{value_display}'>")
                        
                        # Remove the marked tags
                        logging.info(f"FILTER_ELEMENT: About to remove {len(to_remove)} tags")
                        for tag in to_remove:
                            tag_attrs = {attr: tag.get(attr) for attr in ['code', 'displayName']}
                            logging.info(f"FILTER_ELEMENT: Removing tag: {tag_attrs}")
                            try:
                                element.remove(tag)
                                logging.info(f"FILTER_ELEMENT: Successfully removed tag: {tag_attrs}")
                            except Exception as e:
                                logging.error(f"FILTER_ELEMENT: Failed to remove tag: {tag_attrs}, Error: {str(e)}")
                        
                        # Verify removal
                        remaining_tags = element.findall("value")
                        logging.info(f"FILTER_ELEMENT: After removal, {len(remaining_tags)} value tags remain")
                        for tag in remaining_tags:
                            tag_attrs = {attr: tag.get(attr) for attr in ['code', 'displayName']}
                            logging.info(f"FILTER_ELEMENT: Remaining tag: {tag_attrs}")
                        
                        logging.info(f"✅ Final allowed codes for medication status: {allowed_display_names}")
                    else:
                        # For medication name elements, use standard handling
                        for value_tag in value_tags:
                            value_code = value_tag.get("code", "").strip().lower()
                            if value_code not in allowed_set:
                                element.remove(value_tag)
                                logging.info(f"Removed <value code='{value_code}'> (not in allowed list)")
                else:
                    # Standard handling for non-medication sections
                    for value_tag in value_tags:
                        value_code = value_tag.get("code", "").strip().lower()
                        if value_code not in allowed_set:
                            element.remove(value_tag)
                            logging.info(f"Removed <value code='{value_code}'> (not in allowed list)")

                logging.info(f"✅ Final allowed codes: {allowed_display_names}")
                break

def remove_tag_by_condition(xml_tree: ET.ElementTree, tag_name: str, condition: dict, section_condition: dict):
    """
    Removes the first tag with the given tag name and attribute condition from the XML tree.

    Parameters:
        xml_tree (ElementTree): The parsed XML tree.
        tag_name (str): The name of the tag to search for (e.g., 'element', 'value').
        condition (dict): Attributes to match (e.g., {"code": "1234", "displayName": "Race"}).
    """
    removed = False
    sections = get_all_section(xml_tree, section_condition)
    for parent in sections:
        for child in list(parent):  # use list() to avoid mutation during iteration
            if child.tag == tag_name and all(child.get(k) == v for k, v in condition.items()):
                parent.remove(child)
                logging.info(f"Removed <{tag_name}> with attributes: {condition}")
                removed = True
                break
        if removed:
            break

    if not removed:
        logging.info(f"No <{tag_name}> tag found with attributes: {condition}")

def remove_attribute(xml_tree: ET.ElementTree, tag_name: str, attribute: str):
    xml = xml_tree.getroot()

    for element in xml.iter(tag_name):
        attribute_value = element.attrib.get(attribute)

        # Check if this is a BL type value element
        is_bl_type = element.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL"

        if attribute_value is None:
            # Remove attribute if it doesn't exist
            element.attrib.pop(attribute, None)
        elif attribute_value.strip() == "":
            if is_bl_type:
                # For BL types, convert empty string to "false" for valid XML schema
                element.set(attribute, "false")
            else:
                # For non-BL types, remove empty attributes
                element.attrib.pop(attribute, None)

def get_all_section(xmltree: ET.ElementTree | ET.Element, condition: dict, remove: bool = False) -> list[ET.Element]:
    sections = []

    if isinstance(xmltree, ET.Element):
        root = xmltree
    else:
        root = xmltree.getroot()

    # Create a parent map for efficient parent lookup
    parent_map = {c: p for p in root.iter() for c in p}

    section_elements = root.findall(".//section")

    for section in section_elements:
        if all(section.get(attr) == val for attr, val in condition.items()):
            sections.append(section)

    if remove and sections:
        for section in sections:
            parent = parent_map.get(section)
            if parent is not None:
                parent.remove(section)

    return sections
def remove_event_tags(xmltree: ET.ElementTree | ET.Element, condition: dict, section_condition: dict):


    sections = get_all_section(xmltree, section_condition) or [xmltree]

    for section in sections:
        elements = section.findall(".//element")
        section_flag = 0
        for ele in elements:
            all_value = ele.findall(".//value")
            for m_val in all_value:
                if section_flag == 0:
                    med = all(m_val.get(attr) == it_val for attr, it_val in condition.items())
                    if med:
                        section.clear()
                        section_flag = 1
                        break
            if section_flag ==1:
                break
        if section_flag==1:
            break


def handle_table_fields(xmltree: ET.ElementTree, elements, selections, field_id: int, data: dict, key_element: str, section_condition: dict):
    med_logger.info(f"TABLE FIELDS: Called with field_id={field_id}, key_element={key_element}, section={section_condition}")
    
    table_data = data.get(key_element)
    if not table_data: 
        med_logger.info(f"TABLE FIELDS: No data found for key_element={key_element}")
        return

    sections = get_all_section(xmltree, section_condition)
    med_logger.info(f"TABLE FIELDS: Found {len(sections)} matching sections")
    
    # Check if we're dealing with medication sections
    section_code = section_condition.get("code")
    is_medication_section = section_code in ["PREPROCMED", "DCMEDS", "ADJMEDS"]
    med_logger.info(f"TABLE FIELDS: Processing section {section_code}, is medication section: {is_medication_section}")
    
    # If this is a medication section, let's directly find and process all medication status elements first
    if is_medication_section:
        med_logger.info(f"DIRECT APPROACH: Processing medication section {section_code}")
        
        # Find all medication status elements in all matching sections
        for section in sections:
            med_logger.info(f"DIRECT APPROACH: Processing section {section.get('code')}")
            
            # Find the status elements based on section type
            status_display_name = "Pre-Procedure Medication Administered" if section_code == "PREPROCMED" else "Current Medications at Time of Event"
            
            # Find all status elements in this section
            status_elements = section.findall(f".//element[@displayName='{status_display_name}']")
            med_logger.info(f"DIRECT APPROACH: Found {len(status_elements)} status elements with displayName='{status_display_name}'")
            
            # Process each status element
            for status_element in status_elements:
                med_logger.info(f"DIRECT APPROACH: Processing status element: {status_element.get('code')}, {status_element.get('displayName')}")
                
                # Find all value tags in this element
                value_tags = status_element.findall("value")
                med_logger.info(f"DIRECT APPROACH: Found {len(value_tags)} value tags")
                
                # Find the medication element that comes before this status element
                med_element = section.find(f".//element[@displayName='Medication']")
                if med_element is not None:
                    med_value = med_element.find("value")
                    if med_value is not None:
                        med_code = med_value.get("code")
                        med_name = med_value.get("displayName")
                        med_logger.info(f"DIRECT APPROACH: Associated medication: {med_code} - {med_name}")
                        
                        # Find the corresponding value in our data
                        for key, val in table_data.items():
                            if val.get("label") == med_name:
                                selected_value = val.get("value")
                                med_logger.info(f"DIRECT APPROACH: Found matching medication in data: {med_name}, selected value: {selected_value}")
                                
                                # Find the code for this selected value
                                value_selection = selections[(selections['Element Reference'] == int(val.get("field_id"))) & 
                                                           (selections['Selection Name'].str.strip() == selected_value)]
                                
                                if not value_selection.empty:
                                    value_code = str(value_selection.iloc[0]['Code'])
                                    med_logger.info(f"DIRECT APPROACH: Found code for selected value: {value_code}")
                                    
                                    # Keep only the value tag that matches our selected value
                                    to_remove = []
                                    for tag in value_tags:
                                        tag_code = tag.get("code")
                                        tag_name = tag.get("displayName")
                                        
                                        if tag_code != value_code:
                                            to_remove.append(tag)
                                            med_logger.info(f"DIRECT APPROACH: Marking for removal: {tag_code} - {tag_name}")
                                        else:
                                            med_logger.info(f"DIRECT APPROACH: Keeping: {tag_code} - {tag_name}")
                                    
                                    # Remove the non-matching tags
                                    for tag in to_remove:
                                        try:
                                            status_element.remove(tag)
                                            med_logger.info(f"DIRECT APPROACH: Successfully removed: {tag.get('code')} - {tag.get('displayName')}")
                                        except Exception as e:
                                            med_logger.error(f"DIRECT APPROACH: Failed to remove tag: {tag.get('code')}, Error: {str(e)}")
                                    
                                    # Verify the result
                                    remaining = status_element.findall("value")
                                    med_logger.info(f"DIRECT APPROACH: After removal, {len(remaining)} values remain")
                                    for r in remaining:
                                        med_logger.info(f"DIRECT APPROACH: Remaining value: {r.get('code')} - {r.get('displayName')}")
                                    
                                    # Break out of the loop once we've found and processed the matching medication
                                    break
    for key, val in table_data.items():
        dos_id = val.get("field_id")
        value = val.get("value")

        event_date = None
        if 'if_yes' in val and value in ["yes", "Yes", True, "true"]:
            event_date = val.get('if_yes',{}).get("value")

        selection_values = selections[(selections['Element Reference'] == field_id) & (selections['Selection Name'].str.strip() == val.get("label"))]

        if not selection_values.empty:
            selection_values = selection_values.iloc[0]
            medicine = {
                "displayName": str(selection_values['Selection Name']),
                "code": str(selection_values['Code']),
                "codeSystem": str(selection_values['Code System'])
            }
            if not value:
                logging.info(f"Removing... {medicine}")
                remove_event_tags(xmltree, medicine, section_condition)
                continue
            else:
                logging.info(f"value is in the element...")

            if str(field_id) in events:
                for section in sections:
                    elements = section.findall(".//element")
                    section_flag = 0

                    for ele in elements:
                        all_value = ele.findall(".//value")
                        for m_val in all_value:
                            if section_flag == 0:
                                med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                if med:
                                    section_flag = 1
                            else:
                                if m_val.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "BL":
                                    if value is not None and str(value).strip():
                                        m_val.set("value", "true" if value in ["true", "yes", "Yes"] else "false")
                                    else:
                                        m_val.set("value", "false")
                                elif m_val.attrib.get("{http://www.w3.org/2001/XMLSchema-instance}type") == "DT":
                                    m_val.set("value", event_date if event_date else "")
                    if section_flag == 1:
                        break
            else:
                # Special handling for medication sections
                med_logger.info(f"TABLE FIELDS: Checking if medication section: {is_medication_section}")
                if is_medication_section:
                    med_logger.info(f"MEDICATION PROCESSING: Section code: {section_code}, Field ID: {field_id}, DOS ID: {dos_id}, Value: {value}")
                    med_logger.info(f"MEDICATION PROCESSING: Medicine: {medicine}")
                    
                    # For medications, we need to find the value that matches our selection
                    value_selection = None
                    
                    # Handle different medication section types
                    if section_code == "PREPROCMED":
                        # For pre-procedure medications, we're looking for Current/Held/Past/Never
                        med_logger.info(f"MEDICATION PROCESSING: Looking for Pre-Procedure medication status: {value}")
                        value_selection = selections[(selections['Element Reference'] == int(dos_id)) & 
                                                    (selections['Selection Name'].str.strip() == value)]
                        med_logger.info(f"MEDICATION PROCESSING: Found {len(value_selection)} matching selections")
                    elif section_code in ["DCMEDS", "ADJMEDS"]:
                        # For discharge and adjudication medications, we're looking for Yes/No options
                        med_logger.info(f"MEDICATION PROCESSING: Looking for Discharge/Adjudication medication status: {value}")
                        value_selection = selections[(selections['Element Reference'] == int(dos_id)) & 
                                                    (selections['Selection Name'].str.strip() == value)]
                        med_logger.info(f"MEDICATION PROCESSING: Found {len(value_selection)} matching selections")
                    
                    if not value_selection.empty:
                        value_selection = value_selection.iloc[0]
                        value_code = str(value_selection['Code'])
                        med_logger.info(f"MEDICATION PROCESSING: Selected value code: {value_code}")
                        
                        for section in sections:
                            med_logger.info(f"MEDICATION PROCESSING: Processing section: {section.get('code')}, {section.get('displayName')}")
                            elements = section.findall(".//element")
                            med_logger.info(f"MEDICATION PROCESSING: Found {len(elements)} elements in this section")
                            section_flag = 0

                            for ele in elements:
                                ele_attrs = {attr: ele.get(attr) for attr in ['code', 'displayName', 'codeSystem']}
                                med_logger.info(f"MEDICATION PROCESSING: Checking element: {ele_attrs}")
                                
                                # Check if this is the medication element or the status element
                                is_medication_element = ele.get("displayName") == "Medication"
                                is_status_element = (section_code == "PREPROCMED" and ele.get("displayName") == "Pre-Procedure Medication Administered") or \
                                                   (section_code in ["DCMEDS", "ADJMEDS"] and ele.get("displayName") == "Current Medications at Time of Event")
                                
                                med_logger.info(f"MEDICATION PROCESSING: is_medication_element: {is_medication_element}, is_status_element: {is_status_element}")
                                
                                all_value = ele.findall(".//value")
                                med_logger.info(f"MEDICATION PROCESSING: Found {len(all_value)} value tags in this element")
                                
                                for m_val in all_value:
                                    m_val_attrs = {attr: m_val.get(attr) for attr in ['code', 'displayName', 'codeSystem']}
                                    med_logger.info(f"MEDICATION PROCESSING: Checking value: {m_val_attrs}")
                                    
                                    if section_flag == 0 and is_medication_element:
                                        med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                        med_logger.info(f"MEDICATION PROCESSING: Comparing with medicine: {medicine}, match: {med}")
                                        if med:
                                            section_flag = 1
                                            med_logger.info(f"MEDICATION PROCESSING: Found matching medication, setting section_flag = 1")
                                    elif section_flag == 1 and is_status_element:
                                        # For medication status elements, keep only the value that matches our selection
                                        med_logger.info(f"MEDICATION PROCESSING: Checking status value code: {m_val.get('code')} against target: {value_code}")
                                        
                                        # Store values to remove in a separate list to avoid modifying during iteration
                                        to_remove = []
                                        if m_val.get("code") != value_code:
                                            to_remove.append(m_val)
                                            med_logger.info(f"MEDICATION PROCESSING: Marked for removal: {m_val_attrs}")
                                        else:
                                            med_logger.info(f"MEDICATION PROCESSING: Keeping value: {m_val.get('displayName')} (matches {value})")
                                        
                                        # Remove the values after iteration
                                        for tag in to_remove:
                                            try:
                                                ele.remove(tag)
                                                med_logger.info(f"MEDICATION PROCESSING: Successfully removed medication value: {tag.get('displayName')}")
                                            except Exception as e:
                                                med_logger.error(f"MEDICATION PROCESSING: Failed to remove value: {tag.get('code')}, Error: {str(e)}")
                                
                                # After processing all values in this element
                                if section_flag == 1 and is_status_element:
                                    # Find all values and keep only the one matching our target code
                                    all_values = ele.findall("value")
                                    med_logger.info(f"MEDICATION PROCESSING: Final check - found {len(all_values)} values in element")
                                    
                                    # Keep track of which values to remove
                                    to_remove = []
                                    for val in all_values:
                                        if val.get("code") != value_code:
                                            to_remove.append(val)
                                            med_logger.info(f"MEDICATION PROCESSING: Final check - marking for removal: {val.get('code')}, {val.get('displayName')}")
                                    
                                    # Remove the non-matching values
                                    for val in to_remove:
                                        try:
                                            ele.remove(val)
                                            med_logger.info(f"MEDICATION PROCESSING: Final check - removed value: {val.get('code')}, {val.get('displayName')}")
                                        except Exception as e:
                                            med_logger.error(f"MEDICATION PROCESSING: Final check - failed to remove: {val.get('code')}, Error: {str(e)}")
                                    
                                    # Verify the result
                                    remaining = ele.findall("value")
                                    med_logger.info(f"MEDICATION PROCESSING: Final check - {len(remaining)} values remain")
                                    for r in remaining:
                                        med_logger.info(f"MEDICATION PROCESSING: Final check - remaining value: {r.get('code')}, {r.get('displayName')}")
                            
                            if section_flag == 1:
                                break
                else:
                    # Standard handling for non-medication sections
                    value_selection = selections[(selections['Element Reference'] == int(dos_id)) & (selections['Selection Name'].str.strip() == value)]
                    if not value_selection.empty:
                        value_selection = value_selection.iloc[0]
                        value_set = {
                            # "displayName": str(value_selection['Selection Name']),
                            "code": str(value_selection['Code']),
                            "codeSystem": str(value_selection['Code System'])
                        }

                        for section in sections:
                            elements = section.findall(".//element")
                            section_flag = 0

                            for ele in elements:
                                all_value = ele.findall(".//value")
                                for m_val in all_value:
                                    if section_flag == 0:
                                        med = all(m_val.get(attr) == it_val for attr, it_val in medicine.items())
                                        if med:
                                            section_flag = 1
                                    else:
                                        dosage = all(m_val.get(attr) == it_val for attr, it_val in value_set.items())
                                        if not dosage:
                                            ele.remove(m_val)
                            if section_flag == 1:
                                break

def add_element(element: ET.Element, elements: pd.DataFrame, selection: pd.DataFrame, data: dict) -> ET.Element:

    for k, v in data.items():
        field_id = int(v.get("field_id", 0))
        value = v.get("value", 0)
        element_data = elements[elements['Element Reference'] == field_id]


        if element_data.empty:
            logging.info(f"No element mapping found for field_id: {field_id}")
            return
        element_data = element_data.iloc[0]

        condition = {
                "displayName": str(element_data['Name']),
                "code": str(element_data['Code']),
                "codeSystem": str(element_data['Code System'])
            }
        value_type = str(element_data['Data Type'])
        if value_type == 'BL':
            if value is not None and str(value).strip():
                if value in ["Yes", "yes", "true", True]:
                    value = 'true'
                else:
                    value = 'false'
            else:
                value = 'false'
        elif not value:
            logging.info(f"Removing... {condition}")
            remove_event_tags(element, condition, {})
            continue

        if value_type == 'CD':
            selection_values = selection[(selection['Element Reference'] == field_id) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

            if not selection_values.empty:
                codes = selection_values['Code'].to_list()
                filter_element_values_by_code(element, condition, {}, codes)

        else:
            update_xml_element_by_attributes(element, condition, {}, value)
    return element
def add_element_with_device_mapping(element: ET.Element, elements: pd.DataFrame, selection: pd.DataFrame, device_elements: pd.DataFrame, data: dict) -> ET.Element:
    """
    Enhanced version of add_element that uses device_elements.csv for device mapping
    """
    for k, v in data.items():
        field_id = int(v.get("field_id", 0))
        value = v.get("value", 0)
        element_data = elements[elements['Element Reference'] == field_id]

        if element_data.empty:
            logging.info(f"No element mapping found for field_id: {field_id}")
            continue

        element_data = element_data.iloc[0]

        condition = {
            "displayName": str(element_data['Name']),
            "code": str(element_data['Code']),
            "codeSystem": str(element_data['Code System'])
        }

        value_type = str(element_data['Data Type'])

        # Handle boolean values
        if value_type == 'BL':
            if value is not None and str(value).strip():
                if value in ["Yes", "yes", "true", True]:
                    value = 'true'
                else:
                    value = 'false'
            else:
                value = 'false'
        elif not value:
            logging.info(f"Removing... {condition}")
            remove_event_tags(element, condition, {})
            continue

        # Handle coded values - check if this is a device field
        if value_type == 'CD' and field_id == 14841:  # Device ID field
            # Look up the device in device_elements.csv
            device_data = device_elements[
                device_elements['deviceName'].str.strip() == value.strip()
            ]

            if not device_data.empty:
                device_data = device_data.iloc[0]

                # Create Device element with proper NCDR codes
                device_element = create_element('element', {
                    "code": str(element_data['Code']),  # 63653004
                    "codeSystem": str(element_data['Code System']),  # 2.16.840.1.113883.6.96
                    "displayName": str(element_data['Name'])  # Device
                })

                # Create value element with device info from CSV
                device_value_element = create_element('value', {
                    "xsi:type": "CD",
                    "code": str(device_data['deviceID']),
                    "codeSystem": str(device_data['codeSystem']),  # 2.16.840.1.113883.3.3478.6.1.109
                    "displayName": f"{device_data['deviceName']} ({device_data['modelNumber']})"
                })

                device_element.append(device_value_element)
                element.append(device_element)

                logging.info(f"✅ Mapped device: {value} -> deviceID: {device_data['deviceID']}")
                continue
            else:
                logging.warning(f"⚠️ Device '{value}' not found in device_elements.csv")
                # Fall back to original selection-based logic

        # Original logic for non-device CD fields or fallback
        if value_type == 'CD':
            selection_values = selection[(selection['Element Reference'] == field_id) & (selection['Selection Name'].str.strip().isin(value if isinstance(value, list) else [value]))]

            if not selection_values.empty:
                codes = selection_values['Code'].to_list()
                filter_element_values_by_code(element, condition, {}, codes)
        else:
            # For the add_element_with_device_mapping function, we don't have access to unit info, so pass None
            update_xml_element_by_attributes(element, condition, {}, value, None)

    return element


def create_element(tag_name: str, attributes: dict)-> ET.Element:
    return ET.Element(tag_name, attributes)


def normalize_medication_name(name):
    """
    Normalize medication names to handle different formats between XML and database.
    
    Args:
        name (str): The medication name to normalize.
        
    Returns:
        str: The normalized medication name.
    """
    name = name.lower()
    
    # Handle specific medication name mappings
    name_mappings = {
        "heparin derivative": "heparin_derivative",
        "low molecular weight heparin": "low_molecular_weight_heparin",
        "unfractionated heparin": "unfractionated_heparin",
        "aspirin 81 to 100 mg": "aspirin_81_100_mg",
        "aspirin 101 to 324 mg": "aspirin_101_324_mg",
        "aspirin 325 mg": "aspirin_325_mg",
        "aspirin/dipyridamole": "aspirin_dipyridamole",
        "aspirin": "aspirin",  # Special case for plain aspirin
        "warfarin": "warfarin",
        "fondaparinux": "fondaparinux",
        "apixaban": "apixaban",
        "dabigatran": "dabigatran",
        "edoxaban": "edoxaban",
        "rivaroxaban": "rivaroxaban",
        "cangrelor": "cangrelor",
        "clopidogrel": "clopidogrel",
        "prasugrel": "prasugrel",
        "ticagrelor": "ticagrelor",
        "ticlopidine": "ticlopidine",
        "vorapaxar": "vorapaxar"
    }
    
    # Check for exact matches in the mapping
    if name in name_mappings:
        return name_mappings[name]
    
    # Try to match by removing spaces and special characters
    normalized = name.replace(" ", "_").replace("/", "_").replace("-", "_")
    
    # Additional checks for specific medications
    if name == "aspirin" or name == "aspirin (asa)":
        # For plain aspirin, check all possible aspirin types
        return ["aspirin", "aspirin_81_100_mg", "aspirin_101_324_mg", "aspirin_325_mg"]
    elif "aspirin" in name and ("81" in name or "100" in name):
        return "aspirin_81_100_mg"
    elif "aspirin" in name and ("101" in name or "324" in name):
        return "aspirin_101_324_mg"
    elif "aspirin" in name and "325" in name:
        return "aspirin_325_mg"
    elif "aspirin" in name and "dipyridamole" in name:
        return "aspirin_dipyridamole"
    elif "heparin" in name and "derivative" in name:
        return "heparin_derivative"
    elif "low" in name and "molecular" in name and "heparin" in name:
        return "low_molecular_weight_heparin"
    elif "unfractionated" in name and "heparin" in name:
        return "unfractionated_heparin"
    elif "p2y12" in name:
        return "other_p2y12"
    
    return normalized

def process_intra_post_procedure_events(xml_tree, event_values):
    """
    Process intra or post procedure events in the XML using field IDs to lookup codes from CSV files.

    Args:
        xml_tree (ElementTree): The XML tree to modify.
        event_values (dict): Event values from the database.
    """
    import logging
    import pandas as pd
    import os

    event_logger = logging.getLogger("intra_post_procedure_events")

    if not event_values or 'IPPEVENTS' not in event_values:
        event_logger.info("No intra/post procedure events to process")
        return

    event_logger.info("Processing intra/post procedure events")

    # Load CSV files
    base_dir = os.path.abspath("v2/data_sources/ncdr")
    elements_file = os.path.join(base_dir, "elements.csv")
    selections_file = os.path.join(base_dir, "selections.csv")

    try:
        elements_df = pd.read_csv(elements_file, encoding='utf-8')
    except UnicodeDecodeError:
        elements_df = pd.read_csv(elements_file, encoding='latin1')

    try:
        selections_df = pd.read_csv(selections_file, encoding='utf-8')
    except UnicodeDecodeError:
        selections_df = pd.read_csv(selections_file, encoding='latin1')

    event_logger.info(f"Loaded {len(elements_df)} elements and {len(selections_df)} selections")

    # Process each event
    for event_name, event_info in event_values['IPPEVENTS'].items():
        event_value = event_info.get('value', '')
        event_date = event_info.get('date', '')
        event_field_id = event_info.get('field_id', '')

        event_logger.info(f"Processing event: {event_name}")
        event_logger.info(f"  Value: {event_value}")
        event_logger.info(f"  Date: {event_date}")
        event_logger.info(f"  Field ID: {event_field_id}")

        # Skip if no field ID
        if not event_field_id:
            event_logger.info(f"  No field ID for {event_name}, skipping")
            continue

        # Skip if event didn't occur
        if event_value != "Yes":
            event_logger.info(f"  Event {event_name} did not occur, skipping")
            continue

        # Create a new section for this event
        section = ET.Element("section", {
            "code": "IPPEVENTS",
            "displayName": "Intra or Post-Procedure Events"
        })

        # Find element info for field ID 12153 (Intra or Post Procedure Events)
        element_12153 = elements_df[elements_df['Element Reference'] == 12153]
        if element_12153.empty:
            event_logger.error("Could not find element for field ID 12153 in elements.csv")
            continue

        element_12153_row = element_12153.iloc[0]
        element_code = str(element_12153_row['NCDR Code']).strip()
        element_code_system = str(element_12153_row['Code System']).strip()
        element_display_name = str(element_12153_row['Name']).strip()

        event_logger.info(f"Found element code for 'Intra or Post Procedure Events': {element_code}")

        # Find element info for field ID 9002 (Event Occurred)
        element_9002 = elements_df[elements_df['Element Reference'] == 9002]
        if element_9002.empty:
            event_logger.error("Could not find element for 'Event Occurred' in elements.csv")
            continue

        element_9002_row = element_9002.iloc[0]
        occurred_code = str(element_9002_row['NCDR Code']).strip()
        occurred_code_system = str(element_9002_row['Code System']).strip()
        occurred_display_name = str(element_9002_row['Name']).strip()

        event_logger.info(f"Found element code for 'Event Occurred': {occurred_code}")

        # Find selection info for the specific event using field ID 12153 and event name
        # Look for selections with Element Reference 12153 (Intra or Post Procedure Events)
        event_selections = selections_df[selections_df['Element Reference'] == 12153]

        if not event_selections.empty:
            # Try to match the event name with the selection name
            # Convert event name to a more searchable format
            search_name = event_name.replace('_', ' ').title()

            # Try different variations of the event name
            possible_names = [
                search_name,
                event_name.replace('_', ' ').title(),
                event_name.title(),
                event_name.upper(),
                event_name.lower()
            ]

            selection_row = None
            for name_variant in possible_names:
                matching_selection = event_selections[
                    event_selections['Selection Name'].str.contains(name_variant, case=False, na=False)
                ]
                if not matching_selection.empty:
                    selection_row = matching_selection.iloc[0]
                    break

     

            if selection_row is not None:
                selection_code = str(selection_row['Code']).strip()
                selection_code_system = str(selection_row['Code System']).strip()
                selection_display_name = str(selection_row['Selection Name']).strip()

                event_logger.info(f"Found selection for event '{event_name}': {selection_display_name} ({selection_code})")

                # Create the event element
                event_element = ET.Element("element", {
                    "code": element_code,
                    "codeSystem": element_code_system,
                    "displayName": element_display_name
                })

                # Add the event value
                event_value_element = ET.Element("value", {
                    "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                    "code": selection_code,
                    "codeSystem": selection_code_system,
                    "displayName": selection_display_name
                })
                event_element.append(event_value_element)
                section.append(event_element)

                # Create the event occurred element
                occurred_element = ET.Element("element", {
                    "code": occurred_code,
                    "codeSystem": occurred_code_system,
                    "displayName": occurred_display_name
                })

                # Add the occurred value
                occurred_value_element = ET.Element("value", {
                    "{http://www.w3.org/2001/XMLSchema-instance}type": "BL",
                    "value": "true"
                })
                occurred_element.append(occurred_value_element)
                section.append(occurred_element)

                # Add event date if available
                if event_date:
                    # Find element info for field ID 14275 (Event Date)
                    element_14275 = elements_df[elements_df['Element Reference'] == 14275]
                    if not element_14275.empty:
                        element_14275_row = element_14275.iloc[0]
                        date_code = str(element_14275_row['NCDR Code']).strip()
                        date_code_system = str(element_14275_row['Code System']).strip()
                        date_display_name = str(element_14275_row['Name']).strip()

                        date_element = ET.Element("element", {
                            "code": date_code,
                            "codeSystem": date_code_system,
                            "displayName": date_display_name
                        })

                        date_value_element = ET.Element("value", {
                            "{http://www.w3.org/2001/XMLSchema-instance}type": "DT",
                            "value": event_date
                        })
                        date_element.append(date_value_element)
                        section.append(date_element)

                # Add the section to the XML tree
                xml_tree.getroot().append(section)
                event_logger.info(f"  Added event section for {event_name}")

            else:
                event_logger.error(f"Could not find matching selection for event '{event_name}' in field ID 12153 selections")

        else:
            event_logger.error("Could not find any selections for field ID 12153 (Intra or Post Procedure Events)")

def filter_medication_status_elements(xml_tree, field_result=None):
    """
    Directly filter medication status elements in the XML to keep only one value.
    This is a workaround for when we don't have medication data but need to clean up the XML.
    
    Args:
        xml_tree (ElementTree): The XML tree to modify.
        field_result (dict or list): Optional field results from the database.
        
    Returns:
        dict: Event values extracted from field_result.
    """
    # For debugging, write the field_result to a file
    if field_result:
        try:
            import json
            with open("c:/Users/<USER>/Desktop/cm-api/app/logs/field_result_debug.json", "w") as f:
                json.dump(field_result, f, indent=2)
            med_logger.info("Wrote field_result to debug file")
        except Exception as e:
            med_logger.error(f"Error writing debug file: {str(e)}")
    med_logger.info("DIRECT FILTER: Starting direct filtering of medication status elements")
    
    # Load selection data for medication values
    base_dir = os.path.abspath("v2/data_sources/ncdr")
    selections_file = os.path.join(base_dir, "selections.csv")
    elements_file = os.path.join(base_dir, "elements.csv")
    
    try:
        selections = pd.read_csv(selections_file, encoding='utf-8')
    except UnicodeDecodeError:
        selections = pd.read_csv(selections_file, encoding='latin1')
    
    try:
        elements = pd.read_csv(elements_file, encoding='utf-8')
    except UnicodeDecodeError:
        elements = pd.read_csv(elements_file, encoding='latin1')
    
    # Define the medication sections and their status element names and codes
    medication_sections = {
        "PREPROCMED": {
            "displayName": "Pre-Procedure Medication Administered",
            "code": "418902065",
            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
            "field_id": "6985",
            "default": "Past"
        },
        "DCMEDS": {
            "displayName": "Discharge Medication Dose",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "10205",
            "default": "Yes"
        },
        "ADJMEDS": {
            "displayName": "Current Medications at Time of Event",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "14940",
            "default": "Yes"
        },
        "FADJMEDS": {
            "displayName": "Current Medications at Time of Event",
            "code": "432102000",
            "codeSystem": "2.16.840.1.113883.6.96",
            "field_id": "15006",
            "default": "Yes"
        }
    }
    
    # Set up logger for intra/post procedure events
    import logging
    event_logger = logging.getLogger("intra_post_procedure_events")
    event_logger.setLevel(logging.INFO)
    
    # Create a file handler
    try:
        event_handler = logging.FileHandler("c:/Users/<USER>/Desktop/cm-api/app/logs/event_logger.log")
        event_handler.setLevel(logging.INFO)
        
        # Create a formatter
        event_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        event_handler.setFormatter(event_formatter)
        
        # Add the handler to the logger
        event_logger.addHandler(event_handler)
    except Exception as e:
        med_logger.error(f"Error setting up event logger: {str(e)}")
    
    # Extract medication values and event values from field_result if provided
    medication_values = {}
    event_values = {}
    if field_result:
        med_logger.info("MEDICATION VALUES FROM DATABASE:")
        
        # Log all medication names we'll be looking for
        med_logger.info("MEDICATION NAME MAPPINGS:")
        name_mappings = {
            "heparin derivative": "heparin_derivative",
            "low molecular weight heparin": "low_molecular_weight_heparin",
            "unfractionated heparin": "unfractionated_heparin",
            "aspirin 81 to 100 mg": "aspirin_81_100_mg",
            "aspirin 101 to 324 mg": "aspirin_101_324_mg",
            "aspirin 325 mg": "aspirin_325_mg",
            "aspirin/dipyridamole": "aspirin_dipyridamole"
        }
        for xml_name, db_name in name_mappings.items():
            med_logger.info(f"  {xml_name} -> {db_name}")
        
        # Handle the exact JSON structure provided
        try:
            # Extract intra_or_post_procedure_events
            if 'intra_or_post_procedure_events' in field_result:
                event_logger.info("Found intra_or_post_procedure_events")
                events_data = field_result['intra_or_post_procedure_events']
                
                if 'events' in events_data and 'elements' in events_data['events']:
                    event_elements = events_data['events']['elements']
                    event_logger.info(f"Processing intra_or_post_procedure_events elements")
                    
                    # Process each category of events
                    for category_name, category_data in event_elements.items():
                        event_logger.info(f"Processing category: {category_name}")
                        
                        # Process each event in the category
                        for event_name, event_data in category_data.items():
                            # Skip if not a dictionary (some fields might be strings)
                            if not isinstance(event_data, dict):
                                continue
                                
                            event_value = event_data.get('value', '')
                            event_field_id = event_data.get('field_id', '')
                            
                            event_logger.info(f"Event: {event_name}")
                            event_logger.info(f"  Value: {event_value}")
                            event_logger.info(f"  Field ID: {event_field_id}")
                            
                            # Check for event date if event occurred
                            event_date = None
                            if event_value == 'Yes' and 'if_yes' in event_data:
                                event_date = event_data['if_yes'].get('value', '')
                                event_logger.info(f"  Event date: {event_date}")
                            
                            # Store the event data
                            if 'IPPEVENTS' not in event_values:
                                event_values['IPPEVENTS'] = {}
                            
                            event_values['IPPEVENTS'][event_name.lower()] = {
                                "value": event_value,
                                "field_id": event_field_id,
                                "date": event_date
                            }
            
            # Extract pre_procedure_medications
            if 'pre_procedure_medications' in field_result:
                med_logger.info("Found pre_procedure_medications")
                pre_proc = field_result['pre_procedure_medications']
                
                if 'medication' in pre_proc and 'medications' in pre_proc['medication']:
                    meds = pre_proc['medication']['medications']
                    med_logger.info(f"Found {len(meds)} medications in pre_procedure_medications")
                    
                    for med_name, med_data in meds.items():
                        med_value = med_data.get('value', '')
                        med_field_id = med_data.get('field_id', '')
                        
                        med_logger.info(f"PREPROCMED: {med_name}")
                        med_logger.info(f"  Value: {med_value}")
                        med_logger.info(f"  Field ID: {med_field_id}")
                        
                        if med_value:
                            if 'PREPROCMED' not in medication_values:
                                medication_values['PREPROCMED'] = {}
                            
                            medication_values['PREPROCMED'][med_name.lower()] = {
                                "value": med_value,
                                "field_id": med_field_id,
                                "code": None
                            }
            
            # Extract discharge_medications
            if 'discharge_medications' in field_result:
                med_logger.info("Found discharge_medications")
                dc_meds = field_result['discharge_medications']
                
                if 'medication' in dc_meds and 'medications' in dc_meds['medication']:
                    meds = dc_meds['medication']['medications']
                    med_logger.info(f"Found {len(meds)} medications in discharge_medications")
                    
                    for med_name, med_data in meds.items():
                        med_value = med_data.get('value', '')
                        med_field_id = med_data.get('field_id', '')
                        
                        med_logger.info(f"DCMEDS: {med_name}")
                        med_logger.info(f"  Value: {med_value}")
                        med_logger.info(f"  Field ID: {med_field_id}")
                        
                        # Check for if_yes nested fields
                        if 'if_yes' in med_data and med_value == 'Yes':
                            med_logger.info(f"  Found if_yes for {med_name}")
                            
                            # Handle dose for aspirin
                            if med_name.lower() == 'aspirin' and 'dose' in med_data['if_yes']:
                                dose_data = med_data['if_yes']['dose']
                                dose_value = dose_data.get('value', '')
                                dose_field_id = dose_data.get('field_id', '')
                                
                                med_logger.info(f"  Aspirin dose: {dose_value}")
                                med_logger.info(f"  Dose field ID: {dose_field_id}")
                                
                                # Map the dose value to the corresponding medication name
                                if dose_value == "81 - 100 MG":
                                    aspirin_med_name = "aspirin_81_100_mg"
                                elif dose_value == "101 - 324 MG":
                                    aspirin_med_name = "aspirin_101_324_mg"
                                elif dose_value == "325 MG":
                                    aspirin_med_name = "aspirin_325_mg"
                                else:
                                    aspirin_med_name = "aspirin"
                                
                                # Add the aspirin with dose
                                if 'DCMEDS' not in medication_values:
                                    medication_values['DCMEDS'] = {}
                                
                                medication_values['DCMEDS'][aspirin_med_name] = {
                                    "value": med_value,
                                    "field_id": med_field_id,
                                    "code": None
                                }
                                
                                med_logger.info(f"  Added {aspirin_med_name} with value {med_value}")
                            
                        # Add the regular medication
                        if med_value:
                            if 'DCMEDS' not in medication_values:
                                medication_values['DCMEDS'] = {}
                            
                            medication_values['DCMEDS'][med_name.lower()] = {
                                "value": med_value,
                                "field_id": med_field_id,
                                "code": None
                            }
            
            # Extract in_hospital_adjudication
            if 'in_hospital_adjudication' in field_result:
                med_logger.info("Found in_hospital_adjudication")
                adj_meds = field_result['in_hospital_adjudication']
                
                if 'medication' in adj_meds and 'medications' in adj_meds['medication']:
                    meds = adj_meds['medication']['medications']
                    med_logger.info(f"Found {len(meds)} medications in in_hospital_adjudication")
                    
                    for med_name, med_data in meds.items():
                        med_value = med_data.get('value', '')
                        med_field_id = med_data.get('field_id', '')
                        
                        med_logger.info(f"ADJMEDS: {med_name}")
                        med_logger.info(f"  Value: {med_value}")
                        med_logger.info(f"  Field ID: {med_field_id}")
                        
                        if med_value:
                            if 'ADJMEDS' not in medication_values:
                                medication_values['ADJMEDS'] = {}
                            
                            medication_values['ADJMEDS'][med_name.lower()] = {
                                "value": med_value,
                                "field_id": med_field_id,
                                "code": None
                            }
        except Exception as e:
            med_logger.error(f"Error extracting medication values: {str(e)}")
            import traceback
            med_logger.error(traceback.format_exc())
    
    # Process each medication section
    for section_code, section_info in medication_sections.items():
        med_logger.info(f"DIRECT FILTER: Processing section {section_code}")
        
        # Find all sections with this code
        sections = xml_tree.findall(f".//section[@code='{section_code}']")
        med_logger.info(f"DIRECT FILTER: Found {len(sections)} sections with code {section_code}")
        
        # Check if we have any values for this section
        has_values = False
        if section_code in medication_values and medication_values[section_code]:
            # Check if any medication in this section has a value
            for med_name, med_info in medication_values[section_code].items():
                if med_info["value"]:
                    has_values = True
                    break
        
        # If no values for this section, remove any existing status elements
        if not has_values:
            med_logger.info(f"DIRECT FILTER: No values found for section {section_code}, removing status elements")
            for section in sections:
                status_elements = section.findall(f".//element[@displayName='{section_info['displayName']}']")
                for status_element in status_elements:
                    section.remove(status_element)
            continue
            
        # Special handling for DCMEDS - remove all status elements and only add them back if we have values
        if section_code == "DCMEDS" or section_code == "ADJMEDS" or section_code == "FADJMEDS":
            med_logger.info(f"DIRECT FILTER: Special handling for {section_code} section")
            
            # First, remove ALL status elements from ALL sections
            for section in sections:
                # For DCMEDS, we need to handle both displayName values
                if section_code == "DCMEDS":
                    # Try both "Discharge Medication Dose" and "Current Medications at Time of Event"
                    for display_name in ["Discharge Medication Dose", "Current Medications at Time of Event"]:
                        status_elements = section.findall(f".//element[@displayName='{display_name}']")
                        for status_element in status_elements:
                            med_logger.info(f"DIRECT FILTER: Removing status element with displayName '{display_name}' from {section_code}")
                            section.remove(status_element)
                else:
                    status_elements = section.findall(f".//element[@displayName='{section_info['displayName']}']")
                    for status_element in status_elements:
                        med_logger.info(f"DIRECT FILTER: Removing status element from {section_code}")
                        section.remove(status_element)
            
            # Now, process each section and only add status elements for medications with values
            for i, section in enumerate(sections):
                med_logger.info(f"DIRECT FILTER: Processing {section_code} section #{i+1}")
                
                # Find the medication element
                med_elements = section.findall(".//element[@displayName='Medication']")
                
                for j, med_element in enumerate(med_elements):
                    med_value = med_element.find("value")
                    if med_value is not None:
                        med_display_name = med_value.get("displayName", "")
                        med_name = normalize_medication_name(med_display_name)
                        med_logger.info(f"DIRECT FILTER: Processing medication #{j+1}: {med_display_name} (normalized: {med_name})")
                        
                        # Check if we have a value for this specific medication
                        med_info = None
                        if section_code in medication_values:
                            # Try the normalized name first
                            if med_name in medication_values[section_code]:
                                med_info = medication_values[section_code][med_name]
                                med_logger.info(f"DIRECT FILTER: Found medication {med_name} in database")
                            else:
                                # Try alternative names
                                med_logger.info(f"DIRECT FILTER: Medication {med_name} not found directly, trying alternatives")
                                
                                # Try with just the first word (e.g., "Aspirin" for "Aspirin 325 mg")
                                first_word = med_display_name.split()[0].lower()
                                for db_med_name in medication_values[section_code].keys():
                                    if first_word in db_med_name:
                                        med_info = medication_values[section_code][db_med_name]
                                        med_logger.info(f"DIRECT FILTER: Found alternative match: {db_med_name}")
                                        break
                        
                        # Skip if no med_info or no value
                        if not med_info:
                            med_logger.info(f"DIRECT FILTER: No medication info found for {med_name}, skipping")
                            continue
                            
                        # Skip if no value
                        if not med_info.get("value"):
                            med_logger.info(f"DIRECT FILTER: No value for {med_name}, skipping")
                            continue
                        
                        # Now we have med_info and it has a value
                        med_status = med_info["value"]
                        med_code = med_info["code"]
                        med_field_id = med_info["field_id"]
                        
                        med_logger.info(f"DIRECT FILTER: Found value for {med_name}: {med_status}, code: {med_code}")
                        
                        # Create a new status element for this medication
                        med_logger.info(f"DIRECT FILTER: Creating status element for {med_name}")
                        status_element = ET.Element("element", {
                            "code": section_info["code"],
                            "codeSystem": section_info["codeSystem"],
                            "displayName": section_info["displayName"]
                        })
                        
                        # If we don't have a code, look it up
                        if not med_code and med_field_id and med_status:
                            try:
                                # First try to look up the code directly
                                value_selection = selections[(selections['Element Reference'] == int(med_field_id)) & 
                                                           (selections['Selection Name'].str.strip() == med_status)]
                                
                                if not value_selection.empty:
                                    med_code = str(value_selection.iloc[0]['Code'])
                                    med_logger.info(f"DIRECT FILTER: Looked up code for {med_status}: {med_code}")
                            except Exception as e:
                                med_logger.error(f"DIRECT FILTER: Error looking up code: {str(e)}")
                        
                        # Add the selected value
                        if med_code:
                            # Look up the display name for this code
                            try:
                                code_selection = selections[selections['Code'] == int(med_code)]
                                if not code_selection.empty:
                                    display_name = code_selection.iloc[0]['Selection Name'].strip()
                                    med_logger.info(f"DIRECT FILTER: Found display name for code {med_code}: {display_name}")
                                else:
                                    display_name = med_status
                            except Exception as e:
                                med_logger.error(f"DIRECT FILTER: Error looking up display name: {str(e)}")
                                display_name = med_status
                            
                            # Use the code we found with the correct display name
                            value_tag = ET.Element("value", {
                                "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                                "code": med_code,
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": display_name
                            })
                            status_element.append(value_tag)
                            med_logger.info(f"DIRECT FILTER: Added value: {med_code} - {display_name}")
                        else:
                            # If we couldn't find a code, use hardcoded values
                            if section_code == "PREPROCMED":
                                if med_status == "Current":
                                    code = "100000987"
                                    display_name = "Current"
                                elif med_status == "Held":
                                    code = "100001010"
                                    display_name = "Held"
                                elif med_status == "Past":
                                    code = "100001070"
                                    display_name = "Past"
                                elif med_status == "Never":
                                    code = "100001071"
                                    display_name = "Never"
                                else:
                                    code = "100001070"  # Default to Past
                                    display_name = "Past"
                            else:  # DCMEDS, ADJMEDS, FADJMEDS
                                if med_status == "Yes":
                                    code = "100001247"
                                    display_name = "Yes"
                                elif med_status == "No - No Reason":
                                    code = "100001048"
                                    display_name = "No - No Reason"
                                elif med_status == "No - Medical Reason":
                                    code = "100001034"
                                    display_name = "No - Medical Reason"
                                elif med_status == "No - Patient Reason":
                                    code = "100001071"
                                    display_name = "No - Patient Reason"
                                elif med_status == "No":
                                    code = "100001048"
                                    display_name = "No"
                                else:
                                    code = "100001247"  # Default to Yes
                                    display_name = "Yes"
                            
                            value_tag = ET.Element("value", {
                                "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                                "code": code,
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": display_name
                            })
                            status_element.append(value_tag)
                            med_logger.info(f"DIRECT FILTER: Added hardcoded value: {code} - {display_name}")
                        
                        # Add the status element to the section
                        section.append(status_element)
                        
                        # Verify the result
                        med_logger.info(f"DIRECT FILTER: Added status element for {med_name} with value {med_status}")
            
            # Skip the regular processing for these sections
            continue
        
        # Process each section
        for i, section in enumerate(sections):
            med_logger.info(f"DIRECT FILTER: Processing section {section_code} #{i+1}")
            
            # Find all medication elements in this section
            med_elements = section.findall(".//element[@displayName='Medication']")
            med_logger.info(f"DIRECT FILTER: Found {len(med_elements)} medication elements in section {section_code} #{i+1}")
            
            # First, remove all existing status elements
            status_elements = section.findall(f".//element[@displayName='{section_info['displayName']}']")
            for status_element in status_elements:
                section.remove(status_element)
            
            for j, med_element in enumerate(med_elements):
                # Get the medication name
                med_value = med_element.find("value")
                if med_value is not None:
                    med_display_name = med_value.get("displayName", "")
                    med_name = normalize_medication_name(med_display_name)
                    med_logger.info(f"DIRECT FILTER: Processing medication #{j+1}: {med_display_name} (normalized: {med_name}) in section {section_code} #{i+1}")
                    
                    # Check if we have a value for this medication
                    med_info = None
                    if section_code in medication_values:
                        # Handle the case where normalize_medication_name returns a list
                        if isinstance(med_name, list):
                            med_logger.info(f"DIRECT FILTER: Normalized name is a list: {med_name}")
                            # Try each name in the list
                            for name in med_name:
                                if name in medication_values[section_code]:
                                    med_info = medication_values[section_code][name]
                                    med_logger.info(f"DIRECT FILTER: Found medication {name} in database")
                                    break
                        # Try the normalized name first
                        elif med_name in medication_values[section_code]:
                            med_info = medication_values[section_code][med_name]
                            med_logger.info(f"DIRECT FILTER: Found medication {med_name} in database")
                        else:
                            # Try alternative names
                            med_logger.info(f"DIRECT FILTER: Medication {med_name} not found directly, trying alternatives")
                            
                            # Try with just the first word (e.g., "Aspirin" for "Aspirin 325 mg")
                            first_word = med_display_name.split()[0].lower()
                            for db_med_name in medication_values[section_code].keys():
                                if first_word in db_med_name:
                                    med_info = medication_values[section_code][db_med_name]
                                    med_logger.info(f"DIRECT FILTER: Found alternative match: {db_med_name}")
                                    break
                    
                    # Skip if no med_info or no value
                    if not med_info:
                        med_logger.info(f"DIRECT FILTER: No medication info found for {med_name}, skipping")
                        continue
                        
                    # Skip if no value
                    if not med_info.get("value"):
                        med_logger.info(f"DIRECT FILTER: No value for {med_name}, skipping")
                        continue

                    # Now we have med_info and it has a value
                    med_status = med_info["value"]
                    med_code = med_info["code"]
                    med_field_id = med_info["field_id"]

                    # Skip if no value
                    if not med_status:
                        med_logger.info(f"DIRECT FILTER: No value for {med_name}, skipping")
                        continue

                    med_logger.info(f"DIRECT FILTER: Found value for {med_name}: {med_status}, code: {med_code}")

                    # Create a new status element for this medication
                    med_logger.info(f"DIRECT FILTER: Creating status element for {med_name}")
                    status_element = ET.Element("element", {
                        "code": section_info["code"],
                        "codeSystem": section_info["codeSystem"],
                        "displayName": section_info["displayName"]
                    })

                    # If we don't have a code, look it up
                    if not med_code and med_field_id and med_status:
                        try:
                            # First try to look up the code directly
                            value_selection = selections[(selections['Element Reference'] == int(med_field_id)) &
                                                       (selections['Selection Name'].str.strip() == med_status)]

                            if not value_selection.empty:
                                med_code = str(value_selection.iloc[0]['Code'])
                                med_logger.info(f"DIRECT FILTER: Looked up code for {med_status}: {med_code}")
                        except Exception as e:
                            med_logger.error(f"DIRECT FILTER: Error looking up code: {str(e)}")

                    # Add the selected value
                    if med_code:
                        # Look up the display name for this code
                        try:
                            code_selection = selections[selections['Code'] == int(med_code)]
                            if not code_selection.empty:
                                display_name = code_selection.iloc[0]['Selection Name'].strip()
                                med_logger.info(f"DIRECT FILTER: Found display name for code {med_code}: {display_name}")
                            else:
                                display_name = med_status
                        except Exception as e:
                            med_logger.error(f"DIRECT FILTER: Error looking up display name: {str(e)}")
                            display_name = med_status

                        # Use the code we found with the correct display name
                        value_tag = ET.Element("value", {
                            "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                            "code": med_code,
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": display_name
                        })
                        status_element.append(value_tag)
                        med_logger.info(f"DIRECT FILTER: Added value: {med_code} - {display_name}")
                    else:
                        # If we couldn't find a code, use hardcoded values
                        if section_code == "PREPROCMED":
                            if med_status == "Current":
                                code = "100000987"
                                display_name = "Current"
                            elif med_status == "Held":
                                code = "100001010"
                                display_name = "Held"
                            elif med_status == "Past":
                                code = "100001070"
                                display_name = "Past"
                            elif med_status == "Never":
                                code = "100001071"
                                display_name = "Never"
                            else:
                                code = "100001070"  # Default to Past
                                display_name = "Past"
                        else:  # DCMEDS, ADJMEDS, FADJMEDS
                            if med_status == "Yes":
                                code = "100001247"
                                display_name = "Yes"
                            elif med_status == "No - No Reason":
                                code = "100001048"
                                display_name = "No - No Reason"
                            elif med_status == "No - Medical Reason":
                                code = "100001034"
                                display_name = "No - Medical Reason"
                            elif med_status == "No - Patient Reason":
                                code = "100001071"
                                display_name = "No - Patient Reason"
                            elif med_status == "No":
                                code = "100001048"
                                display_name = "No"
                            else:
                                code = "100001247"  # Default to Yes
                                display_name = "Yes"

                        value_tag = ET.Element("value", {
                            "{http://www.w3.org/2001/XMLSchema-instance}type": "CD",
                            "code": code,
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": display_name
                        })
                        status_element.append(value_tag)
                        med_logger.info(f"DIRECT FILTER: Added hardcoded value: {code} - {display_name}")

                    # Add the status element to the section
                    section.append(status_element)

                    # Verify the result
                    med_logger.info(f"DIRECT FILTER: Added status element for {med_name} with value {med_status} in section {section_code} #{i+1}")

def process_ncdr_data(field_result: list[dict], op_file_path: str, original_data=None):
        # --- load all lookup tables ---
        base_dir = os.path.abspath("v2/data_sources/ncdr")

        # Try different encodings for CSV files to handle various file formats
        def read_csv_with_encoding(file_path):
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
            for encoding in encodings:
                try:
                    return pd.read_csv(file_path, encoding=encoding)
                except UnicodeDecodeError:
                    continue
            # If all encodings fail, raise the original error
            return pd.read_csv(file_path)

        selection       = read_csv_with_encoding(os.path.join(base_dir, "selections.csv"))
        elements        = read_csv_with_encoding(os.path.join(base_dir, "elements.csv"))
        access_elements = read_csv_with_encoding(os.path.join(base_dir, "access_system_elements.csv"))
        device_elements = read_csv_with_encoding(os.path.join(base_dir, "device_elements.csv"))

        # parse base XML template
        basexml = ET.parse(os.path.join(base_dir, "generated_registry_document.xml"))
        
        # Make a deep copy of the ADMIN section to ensure it's preserved
        admin_section = None
        admin_sections = basexml.findall(".//section[@code='ADMIN']")
        if admin_sections:
            admin_section = copy.deepcopy(admin_sections[0])

        processed_conditions: list[dict] = []

        # Create a mapping of field_ids that were extracted (these are the ones that should exist)
        extracted_field_ids = {str(field.get("field_id")) for field in field_result if field.get("field_id")}

        # Remove elements from XML template that correspond to fields NOT extracted due to conditional logic
        # This handles cases where if_yes, if_alive, etc. fields should be removed when parent condition is not met
        all_elements = elements[elements["Element Reference"].notna()]
        for _, element_row in all_elements.iterrows():
            element_field_id = str(element_row["Element Reference"])

            # If this field was not extracted, remove it from XML template
            if element_field_id not in extracted_field_ids:
                section_cond = {"code": element_row["Section Code"]}
                elem_cond = {
                    "displayName": str(element_row["Name"]),
                    "code": str(element_row["Code"]),
                    "codeSystem": str(element_row["Code System"])
                }
                remove_tag_by_condition(basexml, "element", elem_cond, section_cond)
                logging.info(f"🗑️ Removed element for field {element_field_id} (not in extracted fields)")

        for fields in field_result:
            field_id = fields.get("field_id")
            value    = fields.get("value")
            logging.info(f"🔎 Processing field_id={field_id!r}")

            # try cast to int; if not numeric and not a special container or table, skip
            try:
                fid = int(field_id)
            except (ValueError, TypeError):
                if field_id not in table_ids and field_id != "14839_container":
                    continue
                fid = None  # keep going for special flows

            # --- Normal single-element updates ---
            if field_id not in table_ids and field_id != "14839_container":
                row = elements[elements["Element Reference"] == fid]
                if row.empty:
                    continue
                meta = row.iloc[0]

                section_cond = {"code": meta["Section Code"]}

                # Handle scientific notation in code field
                code_value = str(meta["Code"])
                if "E+" in code_value or "e+" in code_value:
                    # Convert scientific notation to regular number
                    code_value = f"{float(code_value):.0f}"

                elem_cond = {
                    "displayName": str(meta["Name"]),
                    "code":        code_value,
                    "codeSystem":  str(meta["Code System"])
                }
                if elem_cond in processed_conditions:
                    continue

                dtype = str(meta["Data Type"])
                if dtype == "BL":
                    if value is not None and str(value).strip():
                        vlow = str(value).lower()
                        value = "true" if vlow in ("yes", "true") else "false"
                    else:
                        # For BL type with no value, set to "false" for valid XML schema
                        value = "false"

                if not value and dtype != "BL":
                    remove_tag_by_condition(basexml, "element", elem_cond, section_cond)
                elif dtype == "CD":
                    if isinstance(value, str) and value.strip().lower() in ["alive", "deceased"]:
                        # Directly set the value to "Alive" or "Deceased"
                        value = value.strip().capitalize()
                    else:
                        sel = selection[
                            (selection["Element Reference"] == fid) &
                            (selection["Selection Name"].str.strip().isin(
                                value if isinstance(value, list) else [value]
                            ))
                        ]
                        if not sel.empty:
                            # Special handling for medication status elements
                            is_medication_status_element = False
                            section_code = section_cond.get("code")
                            
                            # Check if this is a medication section and status element
                            if section_code in ["PREPROCMED", "DCMEDS", "ADJMEDS"]:
                                # For PREPROCMED, the element displayName should be "Pre-Procedure Medication Administered"
                                # For DCMEDS and ADJMEDS, it should be "Current Medications at Time of Event"
                                expected_display_name = "Pre-Procedure Medication Administered" if section_code == "PREPROCMED" else "Current Medications at Time of Event"
                                
                                # Check if the element's displayName matches what we expect for this section
                                if elem_cond.get("displayName") == expected_display_name:
                                    is_medication_status_element = True
                                    logging.info(f"MEDICATION STATUS: Found medication status element in {section_code} section")
                                else:
                                    # If the displayName doesn't match, we need to adjust it
                                    logging.info(f"MEDICATION STATUS: Adjusting element condition for {section_code} section")
                                    elem_cond["displayName"] = expected_display_name
                                    is_medication_status_element = True
                            
                            if is_medication_status_element:
                                # Pass the code matching the DB value to keep only that value
                                # and remove all other options
                                code_to_keep = sel["Code"].iloc[0]
                                logging.info(f"MEDICATION STATUS: Found code to keep: {code_to_keep} for value: {value}")
                                logging.info(f"MEDICATION STATUS: Element condition: {elem_cond}, Section condition: {section_cond}")
                                filter_element_values_by_code(basexml, elem_cond, section_cond, [code_to_keep])
                            else:
                                codes = sel["Code"].tolist()
                                filter_element_values_by_code(basexml, elem_cond, section_cond, codes)

                else:
                    # Get unit from elements data if available
                    unit = meta.get("Unit Of Measure", "")
                    unit = unit if unit and str(unit).strip() and str(unit).strip() != "nan" else None
                    update_xml_element_by_attributes(basexml, elem_cond, section_cond, value, unit)

                processed_conditions.append({**elem_cond, "section_code": str(meta["Section Code"])})

            # --- Special 14839_container flow (Access System) ---
            elif field_id == "14839_container":
                items = fields.get("items", [])
                if not items:
                    continue

                # Find the PROCINFO section to add Access Systems to
                proc_info_section = get_all_section(basexml, {"code": "PROCINFO"})
                if not proc_info_section:
                    logging.error("Cannot find PROCINFO section in XML")
                    continue

                parent_container = proc_info_section[0]

                # Remove any existing Access System sections
                get_all_section(parent_container, {"code": "ACCESSSYS"}, remove=True)

                # Process each access system item
                for idx, access in enumerate(items, 1):
                    # Create Access System section
                    access_sys_element = create_element('section', {
                        "code": "ACCESSSYS",
                        "displayName": "Access Systems"
                    })

                    # Process Access System Counter
                    counter_element = create_element('element', {
                        "code": "112000002110",
                        "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                        "displayName": "Access System Counter"
                    })
                    counter_element.append(
                        create_element('value', {
                            "xsi:type": "CTR",
                            "value": str(access.get("access_system_counter", {}).get("value", idx))
                        })
                    )
                    access_sys_element.append(counter_element)

                    # Process Access System
                    access_system = access.get("access_system_counter", {}).get("template", {}).get("access_system", {})
                    access_system_value = access_system.get("value", "")

                    if access_system_value:
                        # Look up the access system in the access_system_elements.csv
                        access_device_data = access_elements[
                            access_elements['deviceName'].str.strip() == access_system_value.strip()
                        ]

                        if not access_device_data.empty:
                            access_device_data = access_device_data.iloc[0]

                            # Create Access System element
                            access_element = create_element('element', {
                                "code": "112000002110",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Access System"
                            })

                            # Create value element with device info
                            model_number = access_device_data.get('modelNumber', '')
                            display_name = access_device_data['deviceName']
                            if model_number:
                                display_name = f"{display_name} ({model_number})"

                            value_element = create_element('value', {
                                "xsi:type": "CD",
                                "code": str(access_device_data['deviceID']),
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1.108",  # Fixed codeSystem for Access Systems
                                "displayName": display_name
                            })

                            access_element.append(value_element)
                            access_sys_element.append(access_element)
                        else:
                            logging.warning(f"⚠️ Access system '{access_system_value}' not found in access_system_elements.csv")

                    # Process devices
                    devices_container = access_system.get("template", {}).get("devices", {})
                    devices_items = devices_container.get("items", [])

                    # If no items, check if there's a template to process
                    if not devices_items and "template" in devices_container:
                        # This is for handling the structure in the template
                        device_templates = devices_container.get("template", [])
                        for dev_idx, dev_template in enumerate(device_templates, 1):
                            device_counter_obj = dev_template.get("device_counter", {})
                            device_template = device_counter_obj.get("template", {})

                            # Create Device section
                            device_sec = create_element('section', {
                                "code": "DEVICES",
                                "displayName": "Devices"
                            })

                            # Add Device Counter
                            device_counter = create_element('element', {
                                "code": "2.16.840.1.113883.3.3478.4.851",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Device Counter"
                            })
                            device_counter.append(
                                create_element('value', {
                                    "xsi:type": "CTR",
                                    "value": str(dev_idx)
                                })
                            )
                            device_sec.append(device_counter)

                            # Add Device element (placeholder for template)
                            device_element = create_element('element', {
                                "code": "63653004",
                                "codeSystem": "2.16.840.1.113883.6.96",
                                "displayName": "Device"
                            })
                            # This would be populated with actual data when items are present
                            device_sec.append(device_element)

                            # Add to Access System section
                            access_sys_element.append(device_sec)

                    # Process actual device items if present
                    for dev_idx, dev in enumerate(devices_items, 1):
                        device_counter_obj = dev.get("device_counter", {})
                        device_template = device_counter_obj.get("template", {})

                        # Create Device section
                        device_sec = create_element('section', {
                            "code": "DEVICES",
                            "displayName": "Devices"
                        })

                        # Add Device Counter
                        device_counter = create_element('element', {
                            "code": "2.16.840.1.113883.3.3478.4.851",
                            "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                            "displayName": "Device Counter"
                        })
                        device_counter.append(
                            create_element('value', {
                                "xsi:type": "CTR",
                                "value": str(device_counter_obj.get("value", dev_idx))
                            })
                        )
                        device_sec.append(device_counter)

                        # Add Device element
                        device_info = device_template.get("device", {})
                        device_value = device_info.get("value", "")
                        if device_value:
                            device_row = device_elements[
                                device_elements['deviceName'].str.strip() == device_value.strip()
                            ]
                            if not device_row.empty:
                                dm = device_row.iloc[0]
                                device_element = create_element('element', {
                                    "code": "63653004",
                                    "codeSystem": "2.16.840.1.113883.6.96",
                                    "displayName": "Device"
                                })

                                model_number = dm.get('modelNumber', '')
                                display_name = dm['deviceName']
                                if model_number:
                                    display_name = f"{display_name} ({model_number})"

                                device_element.append(create_element('value', {
                                    "xsi:type": "CD",
                                    "code": str(dm["deviceID"]),
                                    "codeSystem": "2.16.840.1.113883.3.3478.6.1.109",  # Fixed codeSystem for Devices
                                    "displayName": display_name
                                }))
                                device_sec.append(device_element)
                            else:
                                logging.warning(f"⚠️ Device '{device_value}' not found in device_elements.csv")

                        # Add UDI if present
                        udi_info = device_template.get("udi", {})
                        udi_value = udi_info.get("value", "")
                        if udi_value:
                            udi_element = create_element('element', {
                                "code": "2.16.840.1.113883.3.3719",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Device UDI Direct Identifier"
                            })
                            udi_element.append(create_element('value', {
                                "xsi:type": "ST",
                                "value": udi_value
                            }))
                            device_sec.append(udi_element)

                        # Add LAA Isolation Approach
                        laa_info = device_template.get("laa_isolation_approach", {})
                        laa_approach = laa_info.get("value", "")
                        if laa_approach:
                            laa_element = create_element('element', {
                                "code": "112000002111",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "LAA Isolation Approach"
                            })

                            # Map approach values to codes
                            approach_code = ""
                            approach_code_system = ""

                            if laa_approach == "Epicardial":
                                approach_code = "112000002078"
                                approach_code_system = "2.16.840.1.113883.3.3478.6.1"
                                approach_display = "Epicardial Access"
                            elif laa_approach == "Percutaneous":
                                approach_code = "103388001"
                                approach_code_system = "2.16.840.1.113883.6.96"
                                approach_display = "Percutaneous Approach"

                            if approach_code:
                                laa_element.append(create_element('value', {
                                    "xsi:type": "CD",
                                    "code": approach_code,
                                    "codeSystem": approach_code_system,
                                    "displayName": approach_display
                                }))
                                device_sec.append(laa_element)

                        # Add Device Successfully Deployed
                        deployed_info = device_template.get("device_successfully_deployed", {})
                        deployed = deployed_info.get("value", "")
                        if deployed is not None:
                            deployed_element = create_element('element', {
                                "code": "1000142349",
                                "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                "displayName": "Device Successfully Deployed"
                            })

                            # BL value should be "false" for valid XML schema
                            deployed_element.append(create_element('value', {
                                "xsi:type": "BL",
                                "value": "false"
                            }))
                            device_sec.append(deployed_element)

                        # Add Reason Device Not Deployed Successfully if applicable
                        if deployed == "No":
                            reason_info = deployed_info.get("if_yes", {}).get("reason_device_not_deployed_successfully", {})
                            reason = reason_info.get("value", "")
                            if reason:
                                reason_element = create_element('element', {
                                    "code": "112000001662",
                                    "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                    "displayName": "Reason Device Not Deployed Successfully"
                                })

                                # Map reason values to codes
                                reason_code = ""
                                reason_display = ""

                                if reason == "Deployed, not released":
                                    reason_code = "112000002112"
                                    reason_display = "Device Deployed but not Released"
                                elif reason == "Not deployed":
                                    reason_code = "112000002113"
                                    reason_display = "Device not Deployed"
                                elif reason == "Device retrieved":
                                    reason_code = "112000001838"
                                    reason_display = "Device Retrieval"

                                if reason_code:
                                    reason_element.append(create_element('value', {
                                        "xsi:type": "CD",
                                        "code": reason_code,
                                        "codeSystem": "2.16.840.1.113883.3.3478.6.1",
                                        "displayName": reason_display
                                    }))
                                    device_sec.append(reason_element)

                        # Add Device section to Access System section
                        access_sys_element.append(device_sec)

                    # Add Access System section to parent container
                    parent_container.append(access_sys_element)

            # --- Table-driven flows remain unchanged ---
            else:
                handle_table_fields(
                    basexml,
                    elements,
                    selection,
                    int(field_id),
                    fields,
                    {
                        "6985":  "medications",
                        "12153": "elements",
                        "10200": "medications",
                        "14940": "medications",
                        "11990": "elements",
                        "14948": "event_occurred",
                        "15006": "medications"
                    }.get(field_id, ""),
                    {
                        "code": str(
                            elements.loc[
                                elements["Element Reference"] == int(field_id),
                                "Section Code"
                            ].iloc[0]
                        )
                    }
                )

        # --- final cleanup & write ---
        remove_attribute(basexml, "value", "value")
        remove_empty_tags(basexml, "section")
        remove_empty_tags(basexml, "element")
        remove_tag(basexml, "followup")
        
        # Add the ADMIN section back to the XML tree if it was removed during cleanup
        if admin_section is not None:
            submission = basexml.find(".//submission")
            if submission is not None:
                # Check if the ADMIN section still exists
                existing_admin = submission.find(".//section[@code='ADMIN']")
                if existing_admin is None:
                    # If it doesn't exist, add it back
                    submission.insert(0, admin_section)
            
        # Directly filter medication status elements to keep only one value
        filter_medication_status_elements(basexml, original_data if original_data else field_result)
        
        # Validate the XML before writing
        try:
            validate_xml(basexml)
        except Exception as e:
            logging.warning(f"XML validation warning (will still write file): {str(e)}")
            
        basexml.write(op_file_path, encoding="utf-8", xml_declaration=True)
        
        # Final validation after writing
        try:
            validate_xml(op_file_path)
        except Exception as e:
            logging.warning(f"Final XML validation warning: {str(e)}")  