2025-06-11 18:28:39,749 - INFO - Medication processing log started, writing to C:\Users\<USER>\Desktop\cm-api\app\logs\medication_processing_1749646719.log
2025-06-11 18:30:49,011 - INFO - TABLE FIELDS: Called with field_id=6985, key_element=medications, section={'code': 'PREPROCMED'}
2025-06-11 18:30:49,012 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:30:50,220 - INFO - TABLE FIELDS: Called with field_id=12153, key_element=elements, section={'code': 'IPPEVENTS'}
2025-06-11 18:30:50,220 - INFO - TABLE FIELDS: No data found for key_element=elements
2025-06-11 18:30:50,790 - INFO - TABLE FIELDS: Called with field_id=10200, key_element=medications, section={'code': 'DCMEDS'}
2025-06-11 18:30:50,791 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:30:51,060 - INFO - TABLE FIELDS: Called with field_id=14940, key_element=medications, section={'code': 'ADJMEDS'}
2025-06-11 18:30:51,060 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:30:52,481 - INFO - TABLE FIELDS: Called with field_id=11990, key_element=elements, section={'code': 'FUPMEDS'}
2025-06-11 18:30:52,481 - INFO - TABLE FIELDS: No data found for key_element=elements
2025-06-11 18:30:52,939 - INFO - TABLE FIELDS: Called with field_id=14948, key_element=event_occurred, section={'code': 'FUPEVENTS'}
2025-06-11 18:30:52,940 - INFO - TABLE FIELDS: No data found for key_element=event_occurred
2025-06-11 18:30:53,571 - INFO - TABLE FIELDS: Called with field_id=15006, key_element=medications, section={'code': 'FADJMEDS'}
2025-06-11 18:30:53,571 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:30:54,080 - INFO - Wrote field_result to debug file
2025-06-11 18:30:54,081 - INFO - DIRECT FILTER: Starting direct filtering of medication status elements
2025-06-11 18:30:54,090 - INFO - MEDICATION VALUES FROM DATABASE:
2025-06-11 18:30:54,090 - INFO - MEDICATION NAME MAPPINGS:
2025-06-11 18:30:54,091 - INFO -   heparin derivative -> heparin_derivative
2025-06-11 18:30:54,091 - INFO -   low molecular weight heparin -> low_molecular_weight_heparin
2025-06-11 18:30:54,091 - INFO -   unfractionated heparin -> unfractionated_heparin
2025-06-11 18:30:54,092 - INFO -   aspirin 81 to 100 mg -> aspirin_81_100_mg
2025-06-11 18:30:54,092 - INFO -   aspirin 101 to 324 mg -> aspirin_101_324_mg
2025-06-11 18:30:54,092 - INFO -   aspirin 325 mg -> aspirin_325_mg
2025-06-11 18:30:54,093 - INFO -   aspirin/dipyridamole -> aspirin_dipyridamole
2025-06-11 18:30:54,132 - INFO - Found pre_procedure_medications
2025-06-11 18:30:54,132 - INFO - Found 20 medications in pre_procedure_medications
2025-06-11 18:30:54,132 - INFO - PREPROCMED: fondaparinux
2025-06-11 18:30:54,132 - INFO -   Value: Past
2025-06-11 18:30:54,133 - INFO -   Field ID: 14883
2025-06-11 18:30:54,133 - INFO - PREPROCMED: heparin_derivative
2025-06-11 18:30:54,133 - INFO -   Value: Held
2025-06-11 18:30:54,133 - INFO -   Field ID: 14883
2025-06-11 18:30:54,134 - INFO - PREPROCMED: low_molecular_weight_heparin
2025-06-11 18:30:54,134 - INFO -   Value: Held
2025-06-11 18:30:54,134 - INFO -   Field ID: 14883
2025-06-11 18:30:54,134 - INFO - PREPROCMED: unfractionated_heparin
2025-06-11 18:30:54,134 - INFO -   Value: Held
2025-06-11 18:30:54,135 - INFO -   Field ID: 14883
2025-06-11 18:30:54,135 - INFO - PREPROCMED: warfarin
2025-06-11 18:30:54,135 - INFO -   Value: Held
2025-06-11 18:30:54,135 - INFO -   Field ID: 14883
2025-06-11 18:30:54,135 - INFO - PREPROCMED: aspirin_81_100_mg
2025-06-11 18:30:54,136 - INFO -   Value: Held
2025-06-11 18:30:54,136 - INFO -   Field ID: 14883
2025-06-11 18:30:54,136 - INFO - PREPROCMED: aspirin_101_324_mg
2025-06-11 18:30:54,136 - INFO -   Value: Held
2025-06-11 18:30:54,136 - INFO -   Field ID: 14883
2025-06-11 18:30:54,137 - INFO - PREPROCMED: aspirin_325_mg
2025-06-11 18:30:54,137 - INFO -   Value: Current
2025-06-11 18:30:54,137 - INFO -   Field ID: 14883
2025-06-11 18:30:54,137 - INFO - PREPROCMED: aspirin_dipyridamole
2025-06-11 18:30:54,138 - INFO -   Value: Held
2025-06-11 18:30:54,138 - INFO -   Field ID: 14883
2025-06-11 18:30:54,138 - INFO - PREPROCMED: vorapaxar
2025-06-11 18:30:54,138 - INFO -   Value: Past
2025-06-11 18:30:54,139 - INFO -   Field ID: 14883
2025-06-11 18:30:54,139 - INFO - PREPROCMED: apixaban
2025-06-11 18:30:54,139 - INFO -   Value: Held
2025-06-11 18:30:54,139 - INFO -   Field ID: 14883
2025-06-11 18:30:54,140 - INFO - PREPROCMED: dabigatran
2025-06-11 18:30:54,140 - INFO -   Value: Current
2025-06-11 18:30:54,140 - INFO -   Field ID: 14883
2025-06-11 18:30:54,140 - INFO - PREPROCMED: edoxaban
2025-06-11 18:30:54,141 - INFO -   Value: Held
2025-06-11 18:30:54,141 - INFO -   Field ID: 14883
2025-06-11 18:30:54,141 - INFO - PREPROCMED: rivaroxaban
2025-06-11 18:30:54,142 - INFO -   Value: Held
2025-06-11 18:30:54,142 - INFO -   Field ID: 14883
2025-06-11 18:30:54,142 - INFO - PREPROCMED: cangrelor
2025-06-11 18:30:54,142 - INFO -   Value: Past
2025-06-11 18:30:54,142 - INFO -   Field ID: 14883
2025-06-11 18:30:54,143 - INFO - PREPROCMED: clopidogrel
2025-06-11 18:30:54,143 - INFO -   Value: Current
2025-06-11 18:30:54,143 - INFO -   Field ID: 14883
2025-06-11 18:30:54,143 - INFO - PREPROCMED: other_p2y12
2025-06-11 18:30:54,144 - INFO -   Value: Held
2025-06-11 18:30:54,144 - INFO -   Field ID: 14883
2025-06-11 18:30:54,144 - INFO - PREPROCMED: prasugrel
2025-06-11 18:30:54,144 - INFO -   Value: Current
2025-06-11 18:30:54,144 - INFO -   Field ID: 14883
2025-06-11 18:30:54,145 - INFO - PREPROCMED: ticagrelor
2025-06-11 18:30:54,145 - INFO -   Value: Current
2025-06-11 18:30:54,145 - INFO -   Field ID: 14883
2025-06-11 18:30:54,146 - INFO - PREPROCMED: ticlopidine
2025-06-11 18:30:54,146 - INFO -   Value: Current
2025-06-11 18:30:54,146 - INFO -   Field ID: 14883
2025-06-11 18:30:54,146 - INFO - Found discharge_medications
2025-06-11 18:30:54,146 - INFO - Found 18 medications in discharge_medications
2025-06-11 18:30:54,147 - INFO - DCMEDS: fondaparinux
2025-06-11 18:30:54,147 - INFO -   Value: No - No Reason
2025-06-11 18:30:54,148 - INFO -   Field ID: 10205
2025-06-11 18:30:54,148 - INFO - DCMEDS: heparin_derivative
2025-06-11 18:30:54,148 - INFO -   Value: 
2025-06-11 18:30:54,148 - INFO -   Field ID: 10205
2025-06-11 18:30:54,149 - INFO - DCMEDS: low_molecular_weight_heparin
2025-06-11 18:30:54,149 - INFO -   Value: 
2025-06-11 18:30:54,149 - INFO -   Field ID: 10205
2025-06-11 18:30:54,149 - INFO - DCMEDS: unfractionated_heparin
2025-06-11 18:30:54,150 - INFO -   Value: 
2025-06-11 18:30:54,150 - INFO -   Field ID: 10205
2025-06-11 18:30:54,150 - INFO - DCMEDS: warfarin
2025-06-11 18:30:54,150 - INFO -   Value: 
2025-06-11 18:30:54,151 - INFO -   Field ID: 10205
2025-06-11 18:30:54,151 - INFO - DCMEDS: aspirin
2025-06-11 18:30:54,151 - INFO -   Value: Yes
2025-06-11 18:30:54,151 - INFO -   Field ID: 10205
2025-06-11 18:30:54,151 - INFO -   Found if_yes for aspirin
2025-06-11 18:30:54,152 - INFO -   Aspirin dose: 81 - 100 MG
2025-06-11 18:30:54,152 - INFO -   Dose field ID: 10207
2025-06-11 18:30:54,152 - INFO -   Added aspirin_81_100_mg with value Yes
2025-06-11 18:30:54,152 - INFO - DCMEDS: aspirin_dipyridamole
2025-06-11 18:30:54,153 - INFO -   Value: 
2025-06-11 18:30:54,153 - INFO -   Field ID: 10205
2025-06-11 18:30:54,153 - INFO - DCMEDS: vorapaxar
2025-06-11 18:30:54,153 - INFO -   Value: 
2025-06-11 18:30:54,154 - INFO -   Field ID: 10205
2025-06-11 18:30:54,154 - INFO - DCMEDS: apixaban
2025-06-11 18:30:54,154 - INFO -   Value: 
2025-06-11 18:30:54,154 - INFO -   Field ID: 10205
2025-06-11 18:30:54,155 - INFO - DCMEDS: dabigatran
2025-06-11 18:30:54,155 - INFO -   Value: 
2025-06-11 18:30:54,155 - INFO -   Field ID: 10205
2025-06-11 18:30:54,155 - INFO - DCMEDS: edoxaban
2025-06-11 18:30:54,156 - INFO -   Value: 
2025-06-11 18:30:54,156 - INFO -   Field ID: 10205
2025-06-11 18:30:54,156 - INFO - DCMEDS: rivaroxaban
2025-06-11 18:30:54,156 - INFO -   Value: 
2025-06-11 18:30:54,157 - INFO -   Field ID: 10205
2025-06-11 18:30:54,157 - INFO - DCMEDS: cangrelor
2025-06-11 18:30:54,157 - INFO -   Value: 
2025-06-11 18:30:54,157 - INFO -   Field ID: 10205
2025-06-11 18:30:54,158 - INFO - DCMEDS: clopidogrel
2025-06-11 18:30:54,158 - INFO -   Value: 
2025-06-11 18:30:54,158 - INFO -   Field ID: 10205
2025-06-11 18:30:54,158 - INFO - DCMEDS: other_p2y12
2025-06-11 18:30:54,158 - INFO -   Value: 
2025-06-11 18:30:54,159 - INFO -   Field ID: 10205
2025-06-11 18:30:54,159 - INFO - DCMEDS: prasugrel
2025-06-11 18:30:54,159 - INFO -   Value: 
2025-06-11 18:30:54,159 - INFO -   Field ID: 10205
2025-06-11 18:30:54,159 - INFO - DCMEDS: ticagrelor
2025-06-11 18:30:54,159 - INFO -   Value: 
2025-06-11 18:30:54,160 - INFO -   Field ID: 10205
2025-06-11 18:30:54,160 - INFO - DCMEDS: ticlopidine
2025-06-11 18:30:54,160 - INFO -   Value: 
2025-06-11 18:30:54,160 - INFO -   Field ID: 10205
2025-06-11 18:30:54,161 - INFO - Found in_hospital_adjudication
2025-06-11 18:30:54,161 - INFO - Found 20 medications in in_hospital_adjudication
2025-06-11 18:30:54,161 - INFO - ADJMEDS: fondaparinux
2025-06-11 18:30:54,161 - INFO -   Value: Yes
2025-06-11 18:30:54,161 - INFO -   Field ID: 14941
2025-06-11 18:30:54,162 - INFO - ADJMEDS: heparin_derivative
2025-06-11 18:30:54,162 - INFO -   Value: Yes
2025-06-11 18:30:54,162 - INFO -   Field ID: 14941
2025-06-11 18:30:54,163 - INFO - ADJMEDS: low_molecular_weight_heparin
2025-06-11 18:30:54,163 - INFO -   Value: Yes
2025-06-11 18:30:54,163 - INFO -   Field ID: 14941
2025-06-11 18:30:54,163 - INFO - ADJMEDS: unfractionated_heparin
2025-06-11 18:30:54,163 - INFO -   Value: Yes
2025-06-11 18:30:54,163 - INFO -   Field ID: 14941
2025-06-11 18:30:54,164 - INFO - ADJMEDS: warfarin
2025-06-11 18:30:54,164 - INFO -   Value: No
2025-06-11 18:30:54,164 - INFO -   Field ID: 14941
2025-06-11 18:30:54,164 - INFO - ADJMEDS: aspirin_81_100_mg
2025-06-11 18:30:54,165 - INFO -   Value: Yes
2025-06-11 18:30:54,165 - INFO -   Field ID: 14941
2025-06-11 18:30:54,165 - INFO - ADJMEDS: aspirin_101_324_mg
2025-06-11 18:30:54,165 - INFO -   Value: Yes
2025-06-11 18:30:54,165 - INFO -   Field ID: 14941
2025-06-11 18:30:54,166 - INFO - ADJMEDS: aspirin_325_mg
2025-06-11 18:30:54,166 - INFO -   Value: Yes
2025-06-11 18:30:54,166 - INFO -   Field ID: 14941
2025-06-11 18:30:54,166 - INFO - ADJMEDS: aspirin_dipyridamole
2025-06-11 18:30:54,166 - INFO -   Value: Yes
2025-06-11 18:30:54,167 - INFO -   Field ID: 14941
2025-06-11 18:30:54,167 - INFO - ADJMEDS: vorapaxar
2025-06-11 18:30:54,167 - INFO -   Value: Yes
2025-06-11 18:30:54,167 - INFO -   Field ID: 14941
2025-06-11 18:30:54,167 - INFO - ADJMEDS: apixaban
2025-06-11 18:30:54,168 - INFO -   Value: Yes
2025-06-11 18:30:54,168 - INFO -   Field ID: 14941
2025-06-11 18:30:54,168 - INFO - ADJMEDS: dabigatran
2025-06-11 18:30:54,168 - INFO -   Value: Yes
2025-06-11 18:30:54,168 - INFO -   Field ID: 14941
2025-06-11 18:30:54,169 - INFO - ADJMEDS: edoxaban
2025-06-11 18:30:54,169 - INFO -   Value: Yes
2025-06-11 18:30:54,169 - INFO -   Field ID: 14941
2025-06-11 18:30:54,169 - INFO - ADJMEDS: rivaroxaban
2025-06-11 18:30:54,169 - INFO -   Value: Yes
2025-06-11 18:30:54,170 - INFO -   Field ID: 14941
2025-06-11 18:30:54,170 - INFO - ADJMEDS: cangrelor
2025-06-11 18:30:54,170 - INFO -   Value: Yes
2025-06-11 18:30:54,170 - INFO -   Field ID: 14941
2025-06-11 18:30:54,171 - INFO - ADJMEDS: clopidogrel
2025-06-11 18:30:54,171 - INFO -   Value: Yes
2025-06-11 18:30:54,171 - INFO -   Field ID: 14941
2025-06-11 18:30:54,171 - INFO - ADJMEDS: other_p2y12
2025-06-11 18:30:54,172 - INFO -   Value: Yes
2025-06-11 18:30:54,172 - INFO -   Field ID: 14941
2025-06-11 18:30:54,172 - INFO - ADJMEDS: prasugrel
2025-06-11 18:30:54,172 - INFO -   Value: Yes
2025-06-11 18:30:54,172 - INFO -   Field ID: 14941
2025-06-11 18:30:54,173 - INFO - ADJMEDS: ticagrelor
2025-06-11 18:30:54,173 - INFO -   Value: Yes
2025-06-11 18:30:54,173 - INFO -   Field ID: 14941
2025-06-11 18:30:54,174 - INFO - ADJMEDS: ticlopidine
2025-06-11 18:30:54,174 - INFO -   Value: Yes
2025-06-11 18:30:54,174 - INFO -   Field ID: 14941
2025-06-11 18:30:54,175 - INFO - DIRECT FILTER: Processing section PREPROCMED
2025-06-11 18:30:54,175 - INFO - DIRECT FILTER: Found 20 sections with code PREPROCMED
2025-06-11 18:30:54,175 - INFO - DIRECT FILTER: Processing section PREPROCMED #1
2025-06-11 18:30:54,176 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #1
2025-06-11 18:30:54,176 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux) in section PREPROCMED #1
2025-06-11 18:30:54,176 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:30:54,176 - INFO - DIRECT FILTER: Found value for fondaparinux: Past, code: None
2025-06-11 18:30:54,177 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:30:54,179 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:30:54,180 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:30:54,180 - INFO - DIRECT FILTER: Added status element for fondaparinux with value Past in section PREPROCMED #1
2025-06-11 18:30:54,181 - INFO - DIRECT FILTER: Processing section PREPROCMED #2
2025-06-11 18:30:54,181 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #2
2025-06-11 18:30:54,181 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative) in section PREPROCMED #2
2025-06-11 18:30:54,182 - INFO - DIRECT FILTER: Found medication heparin_derivative in database
2025-06-11 18:30:54,182 - INFO - DIRECT FILTER: Found value for heparin_derivative: Held, code: None
2025-06-11 18:30:54,182 - INFO - DIRECT FILTER: Creating status element for heparin_derivative
2025-06-11 18:30:54,184 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,185 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,185 - INFO - DIRECT FILTER: Added status element for heparin_derivative with value Held in section PREPROCMED #2
2025-06-11 18:30:54,185 - INFO - DIRECT FILTER: Processing section PREPROCMED #3
2025-06-11 18:30:54,186 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #3
2025-06-11 18:30:54,186 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin) in section PREPROCMED #3
2025-06-11 18:30:54,186 - INFO - DIRECT FILTER: Found medication low_molecular_weight_heparin in database
2025-06-11 18:30:54,187 - INFO - DIRECT FILTER: Found value for low_molecular_weight_heparin: Held, code: None
2025-06-11 18:30:54,187 - INFO - DIRECT FILTER: Creating status element for low_molecular_weight_heparin
2025-06-11 18:30:54,189 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,190 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,190 - INFO - DIRECT FILTER: Added status element for low_molecular_weight_heparin with value Held in section PREPROCMED #3
2025-06-11 18:30:54,191 - INFO - DIRECT FILTER: Processing section PREPROCMED #4
2025-06-11 18:30:54,192 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #4
2025-06-11 18:30:54,192 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin) in section PREPROCMED #4
2025-06-11 18:30:54,193 - INFO - DIRECT FILTER: Found medication unfractionated_heparin in database
2025-06-11 18:30:54,193 - INFO - DIRECT FILTER: Found value for unfractionated_heparin: Held, code: None
2025-06-11 18:30:54,193 - INFO - DIRECT FILTER: Creating status element for unfractionated_heparin
2025-06-11 18:30:54,195 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,197 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,197 - INFO - DIRECT FILTER: Added status element for unfractionated_heparin with value Held in section PREPROCMED #4
2025-06-11 18:30:54,197 - INFO - DIRECT FILTER: Processing section PREPROCMED #5
2025-06-11 18:30:54,198 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #5
2025-06-11 18:30:54,198 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin) in section PREPROCMED #5
2025-06-11 18:30:54,198 - INFO - DIRECT FILTER: Found medication warfarin in database
2025-06-11 18:30:54,199 - INFO - DIRECT FILTER: Found value for warfarin: Held, code: None
2025-06-11 18:30:54,199 - INFO - DIRECT FILTER: Creating status element for warfarin
2025-06-11 18:30:54,201 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,201 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,202 - INFO - DIRECT FILTER: Added status element for warfarin with value Held in section PREPROCMED #5
2025-06-11 18:30:54,202 - INFO - DIRECT FILTER: Processing section PREPROCMED #6
2025-06-11 18:30:54,203 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #6
2025-06-11 18:30:54,203 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 81 to 100 mg (normalized: aspirin_81_100_mg) in section PREPROCMED #6
2025-06-11 18:30:54,203 - INFO - DIRECT FILTER: Found medication aspirin_81_100_mg in database
2025-06-11 18:30:54,204 - INFO - DIRECT FILTER: Found value for aspirin_81_100_mg: Held, code: None
2025-06-11 18:30:54,204 - INFO - DIRECT FILTER: Creating status element for aspirin_81_100_mg
2025-06-11 18:30:54,206 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,206 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,206 - INFO - DIRECT FILTER: Added status element for aspirin_81_100_mg with value Held in section PREPROCMED #6
2025-06-11 18:30:54,207 - INFO - DIRECT FILTER: Processing section PREPROCMED #7
2025-06-11 18:30:54,207 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #7
2025-06-11 18:30:54,207 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 101 to 324 mg (normalized: aspirin_101_324_mg) in section PREPROCMED #7
2025-06-11 18:30:54,208 - INFO - DIRECT FILTER: Found medication aspirin_101_324_mg in database
2025-06-11 18:30:54,208 - INFO - DIRECT FILTER: Found value for aspirin_101_324_mg: Held, code: None
2025-06-11 18:30:54,208 - INFO - DIRECT FILTER: Creating status element for aspirin_101_324_mg
2025-06-11 18:30:54,210 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,211 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,211 - INFO - DIRECT FILTER: Added status element for aspirin_101_324_mg with value Held in section PREPROCMED #7
2025-06-11 18:30:54,211 - INFO - DIRECT FILTER: Processing section PREPROCMED #8
2025-06-11 18:30:54,211 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #8
2025-06-11 18:30:54,212 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 325 mg (normalized: aspirin_325_mg) in section PREPROCMED #8
2025-06-11 18:30:54,212 - INFO - DIRECT FILTER: Found medication aspirin_325_mg in database
2025-06-11 18:30:54,212 - INFO - DIRECT FILTER: Found value for aspirin_325_mg: Current, code: None
2025-06-11 18:30:54,213 - INFO - DIRECT FILTER: Creating status element for aspirin_325_mg
2025-06-11 18:30:54,214 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:30:54,215 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:30:54,215 - INFO - DIRECT FILTER: Added status element for aspirin_325_mg with value Current in section PREPROCMED #8
2025-06-11 18:30:54,216 - INFO - DIRECT FILTER: Processing section PREPROCMED #9
2025-06-11 18:30:54,216 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #9
2025-06-11 18:30:54,216 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole) in section PREPROCMED #9
2025-06-11 18:30:54,217 - INFO - DIRECT FILTER: Found medication aspirin_dipyridamole in database
2025-06-11 18:30:54,217 - INFO - DIRECT FILTER: Found value for aspirin_dipyridamole: Held, code: None
2025-06-11 18:30:54,217 - INFO - DIRECT FILTER: Creating status element for aspirin_dipyridamole
2025-06-11 18:30:54,219 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,220 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,220 - INFO - DIRECT FILTER: Added status element for aspirin_dipyridamole with value Held in section PREPROCMED #9
2025-06-11 18:30:54,221 - INFO - DIRECT FILTER: Processing section PREPROCMED #10
2025-06-11 18:30:54,221 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #10
2025-06-11 18:30:54,221 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar) in section PREPROCMED #10
2025-06-11 18:30:54,222 - INFO - DIRECT FILTER: Found medication vorapaxar in database
2025-06-11 18:30:54,222 - INFO - DIRECT FILTER: Found value for vorapaxar: Past, code: None
2025-06-11 18:30:54,222 - INFO - DIRECT FILTER: Creating status element for vorapaxar
2025-06-11 18:30:54,224 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:30:54,225 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:30:54,225 - INFO - DIRECT FILTER: Added status element for vorapaxar with value Past in section PREPROCMED #10
2025-06-11 18:30:54,226 - INFO - DIRECT FILTER: Processing section PREPROCMED #11
2025-06-11 18:30:54,226 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #11
2025-06-11 18:30:54,226 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban) in section PREPROCMED #11
2025-06-11 18:30:54,227 - INFO - DIRECT FILTER: Found medication apixaban in database
2025-06-11 18:30:54,227 - INFO - DIRECT FILTER: Found value for apixaban: Held, code: None
2025-06-11 18:30:54,227 - INFO - DIRECT FILTER: Creating status element for apixaban
2025-06-11 18:30:54,229 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,230 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,230 - INFO - DIRECT FILTER: Added status element for apixaban with value Held in section PREPROCMED #11
2025-06-11 18:30:54,230 - INFO - DIRECT FILTER: Processing section PREPROCMED #12
2025-06-11 18:30:54,231 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #12
2025-06-11 18:30:54,231 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran) in section PREPROCMED #12
2025-06-11 18:30:54,231 - INFO - DIRECT FILTER: Found medication dabigatran in database
2025-06-11 18:30:54,231 - INFO - DIRECT FILTER: Found value for dabigatran: Current, code: None
2025-06-11 18:30:54,232 - INFO - DIRECT FILTER: Creating status element for dabigatran
2025-06-11 18:30:54,233 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:30:54,234 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:30:54,234 - INFO - DIRECT FILTER: Added status element for dabigatran with value Current in section PREPROCMED #12
2025-06-11 18:30:54,234 - INFO - DIRECT FILTER: Processing section PREPROCMED #13
2025-06-11 18:30:54,235 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #13
2025-06-11 18:30:54,235 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban) in section PREPROCMED #13
2025-06-11 18:30:54,235 - INFO - DIRECT FILTER: Found medication edoxaban in database
2025-06-11 18:30:54,236 - INFO - DIRECT FILTER: Found value for edoxaban: Held, code: None
2025-06-11 18:30:54,236 - INFO - DIRECT FILTER: Creating status element for edoxaban
2025-06-11 18:30:54,237 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,238 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,238 - INFO - DIRECT FILTER: Added status element for edoxaban with value Held in section PREPROCMED #13
2025-06-11 18:30:54,239 - INFO - DIRECT FILTER: Processing section PREPROCMED #14
2025-06-11 18:30:54,239 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #14
2025-06-11 18:30:54,239 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban) in section PREPROCMED #14
2025-06-11 18:30:54,240 - INFO - DIRECT FILTER: Found medication rivaroxaban in database
2025-06-11 18:30:54,240 - INFO - DIRECT FILTER: Found value for rivaroxaban: Held, code: None
2025-06-11 18:30:54,240 - INFO - DIRECT FILTER: Creating status element for rivaroxaban
2025-06-11 18:30:54,242 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,243 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,243 - INFO - DIRECT FILTER: Added status element for rivaroxaban with value Held in section PREPROCMED #14
2025-06-11 18:30:54,243 - INFO - DIRECT FILTER: Processing section PREPROCMED #15
2025-06-11 18:30:54,243 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #15
2025-06-11 18:30:54,244 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor) in section PREPROCMED #15
2025-06-11 18:30:54,244 - INFO - DIRECT FILTER: Found medication cangrelor in database
2025-06-11 18:30:54,244 - INFO - DIRECT FILTER: Found value for cangrelor: Past, code: None
2025-06-11 18:30:54,245 - INFO - DIRECT FILTER: Creating status element for cangrelor
2025-06-11 18:30:54,246 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:30:54,247 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:30:54,247 - INFO - DIRECT FILTER: Added status element for cangrelor with value Past in section PREPROCMED #15
2025-06-11 18:30:54,248 - INFO - DIRECT FILTER: Processing section PREPROCMED #16
2025-06-11 18:30:54,248 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #16
2025-06-11 18:30:54,248 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel) in section PREPROCMED #16
2025-06-11 18:30:54,249 - INFO - DIRECT FILTER: Found medication clopidogrel in database
2025-06-11 18:30:54,249 - INFO - DIRECT FILTER: Found value for clopidogrel: Current, code: None
2025-06-11 18:30:54,249 - INFO - DIRECT FILTER: Creating status element for clopidogrel
2025-06-11 18:30:54,251 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:30:54,251 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:30:54,252 - INFO - DIRECT FILTER: Added status element for clopidogrel with value Current in section PREPROCMED #16
2025-06-11 18:30:54,252 - INFO - DIRECT FILTER: Processing section PREPROCMED #17
2025-06-11 18:30:54,252 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #17
2025-06-11 18:30:54,253 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12) in section PREPROCMED #17
2025-06-11 18:30:54,253 - INFO - DIRECT FILTER: Found medication other_p2y12 in database
2025-06-11 18:30:54,253 - INFO - DIRECT FILTER: Found value for other_p2y12: Held, code: None
2025-06-11 18:30:54,253 - INFO - DIRECT FILTER: Creating status element for other_p2y12
2025-06-11 18:30:54,255 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:30:54,255 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:30:54,256 - INFO - DIRECT FILTER: Added status element for other_p2y12 with value Held in section PREPROCMED #17
2025-06-11 18:30:54,256 - INFO - DIRECT FILTER: Processing section PREPROCMED #18
2025-06-11 18:30:54,256 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #18
2025-06-11 18:30:54,256 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel) in section PREPROCMED #18
2025-06-11 18:30:54,257 - INFO - DIRECT FILTER: Found medication prasugrel in database
2025-06-11 18:30:54,257 - INFO - DIRECT FILTER: Found value for prasugrel: Current, code: None
2025-06-11 18:30:54,257 - INFO - DIRECT FILTER: Creating status element for prasugrel
2025-06-11 18:30:54,258 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:30:54,259 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:30:54,259 - INFO - DIRECT FILTER: Added status element for prasugrel with value Current in section PREPROCMED #18
2025-06-11 18:30:54,259 - INFO - DIRECT FILTER: Processing section PREPROCMED #19
2025-06-11 18:30:54,260 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #19
2025-06-11 18:30:54,260 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor) in section PREPROCMED #19
2025-06-11 18:30:54,260 - INFO - DIRECT FILTER: Found medication ticagrelor in database
2025-06-11 18:30:54,260 - INFO - DIRECT FILTER: Found value for ticagrelor: Current, code: None
2025-06-11 18:30:54,261 - INFO - DIRECT FILTER: Creating status element for ticagrelor
2025-06-11 18:30:54,262 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:30:54,263 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:30:54,264 - INFO - DIRECT FILTER: Added status element for ticagrelor with value Current in section PREPROCMED #19
2025-06-11 18:30:54,264 - INFO - DIRECT FILTER: Processing section PREPROCMED #20
2025-06-11 18:30:54,264 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #20
2025-06-11 18:30:54,265 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine) in section PREPROCMED #20
2025-06-11 18:30:54,265 - INFO - DIRECT FILTER: Found medication ticlopidine in database
2025-06-11 18:30:54,265 - INFO - DIRECT FILTER: Found value for ticlopidine: Current, code: None
2025-06-11 18:30:54,265 - INFO - DIRECT FILTER: Creating status element for ticlopidine
2025-06-11 18:30:54,267 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:30:54,268 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:30:54,268 - INFO - DIRECT FILTER: Added status element for ticlopidine with value Current in section PREPROCMED #20
2025-06-11 18:30:54,268 - INFO - DIRECT FILTER: Processing section DCMEDS
2025-06-11 18:30:54,268 - INFO - DIRECT FILTER: Found 18 sections with code DCMEDS
2025-06-11 18:30:54,268 - INFO - DIRECT FILTER: Special handling for DCMEDS section
2025-06-11 18:30:54,269 - INFO - DIRECT FILTER: Removing status element with displayName 'Discharge Medication Dose' from DCMEDS
2025-06-11 18:30:54,269 - INFO - DIRECT FILTER: Processing DCMEDS section #1
2025-06-11 18:30:54,269 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux)
2025-06-11 18:30:54,269 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:30:54,270 - INFO - DIRECT FILTER: Found value for fondaparinux: No - No Reason, code: None
2025-06-11 18:30:54,270 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:30:54,272 - INFO - DIRECT FILTER: Looked up code for No - No Reason: 100001048
2025-06-11 18:30:54,273 - INFO - DIRECT FILTER: Added value: 100001048 - No - No Reason
2025-06-11 18:30:54,273 - INFO - DIRECT FILTER: Added status element for fondaparinux with value No - No Reason
2025-06-11 18:30:54,273 - INFO - DIRECT FILTER: Processing DCMEDS section #2
2025-06-11 18:30:54,274 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative)
2025-06-11 18:30:54,274 - INFO - DIRECT FILTER: Medication heparin_derivative not found directly, trying alternatives
2025-06-11 18:30:54,274 - INFO - DIRECT FILTER: No medication info found for heparin_derivative, skipping
2025-06-11 18:30:54,274 - INFO - DIRECT FILTER: Processing DCMEDS section #3
2025-06-11 18:30:54,275 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin)
2025-06-11 18:30:54,275 - INFO - DIRECT FILTER: Medication low_molecular_weight_heparin not found directly, trying alternatives
2025-06-11 18:30:54,275 - INFO - DIRECT FILTER: No medication info found for low_molecular_weight_heparin, skipping
2025-06-11 18:30:54,276 - INFO - DIRECT FILTER: Processing DCMEDS section #4
2025-06-11 18:30:54,276 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin)
2025-06-11 18:30:54,276 - INFO - DIRECT FILTER: Medication unfractionated_heparin not found directly, trying alternatives
2025-06-11 18:30:54,276 - INFO - DIRECT FILTER: No medication info found for unfractionated_heparin, skipping
2025-06-11 18:30:54,276 - INFO - DIRECT FILTER: Processing DCMEDS section #5
2025-06-11 18:30:54,277 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin)
2025-06-11 18:30:54,277 - INFO - DIRECT FILTER: Medication warfarin not found directly, trying alternatives
2025-06-11 18:30:54,277 - INFO - DIRECT FILTER: No medication info found for warfarin, skipping
2025-06-11 18:30:54,278 - INFO - DIRECT FILTER: Processing DCMEDS section #6
2025-06-11 18:30:54,278 - INFO - DIRECT FILTER: Processing medication #1: Aspirin (normalized: aspirin)
2025-06-11 18:30:54,278 - INFO - DIRECT FILTER: Found medication aspirin in database
2025-06-11 18:30:54,278 - INFO - DIRECT FILTER: Found value for aspirin: Yes, code: None
2025-06-11 18:30:54,279 - INFO - DIRECT FILTER: Creating status element for aspirin
2025-06-11 18:30:54,280 - INFO - DIRECT FILTER: Looked up code for Yes: 100001247
2025-06-11 18:30:54,281 - INFO - DIRECT FILTER: Added value: 100001247 - Yes
2025-06-11 18:30:54,281 - INFO - DIRECT FILTER: Added status element for aspirin with value Yes
2025-06-11 18:30:54,281 - INFO - DIRECT FILTER: Processing DCMEDS section #7
2025-06-11 18:30:54,282 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole)
2025-06-11 18:30:54,282 - INFO - DIRECT FILTER: Medication aspirin_dipyridamole not found directly, trying alternatives
2025-06-11 18:30:54,282 - INFO - DIRECT FILTER: No medication info found for aspirin_dipyridamole, skipping
2025-06-11 18:30:54,283 - INFO - DIRECT FILTER: Processing DCMEDS section #8
2025-06-11 18:30:54,283 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar)
2025-06-11 18:30:54,283 - INFO - DIRECT FILTER: Medication vorapaxar not found directly, trying alternatives
2025-06-11 18:30:54,283 - INFO - DIRECT FILTER: No medication info found for vorapaxar, skipping
2025-06-11 18:30:54,284 - INFO - DIRECT FILTER: Processing DCMEDS section #9
2025-06-11 18:30:54,284 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban)
2025-06-11 18:30:54,284 - INFO - DIRECT FILTER: Medication apixaban not found directly, trying alternatives
2025-06-11 18:30:54,284 - INFO - DIRECT FILTER: No medication info found for apixaban, skipping
2025-06-11 18:30:54,285 - INFO - DIRECT FILTER: Processing DCMEDS section #10
2025-06-11 18:30:54,285 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran)
2025-06-11 18:30:54,285 - INFO - DIRECT FILTER: Medication dabigatran not found directly, trying alternatives
2025-06-11 18:30:54,285 - INFO - DIRECT FILTER: No medication info found for dabigatran, skipping
2025-06-11 18:30:54,285 - INFO - DIRECT FILTER: Processing DCMEDS section #11
2025-06-11 18:30:54,286 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban)
2025-06-11 18:30:54,286 - INFO - DIRECT FILTER: Medication edoxaban not found directly, trying alternatives
2025-06-11 18:30:54,286 - INFO - DIRECT FILTER: No medication info found for edoxaban, skipping
2025-06-11 18:30:54,286 - INFO - DIRECT FILTER: Processing DCMEDS section #12
2025-06-11 18:30:54,286 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban)
2025-06-11 18:30:54,287 - INFO - DIRECT FILTER: Medication rivaroxaban not found directly, trying alternatives
2025-06-11 18:30:54,287 - INFO - DIRECT FILTER: No medication info found for rivaroxaban, skipping
2025-06-11 18:30:54,287 - INFO - DIRECT FILTER: Processing DCMEDS section #13
2025-06-11 18:30:54,288 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor)
2025-06-11 18:30:54,288 - INFO - DIRECT FILTER: Medication cangrelor not found directly, trying alternatives
2025-06-11 18:30:54,288 - INFO - DIRECT FILTER: No medication info found for cangrelor, skipping
2025-06-11 18:30:54,288 - INFO - DIRECT FILTER: Processing DCMEDS section #14
2025-06-11 18:30:54,289 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel)
2025-06-11 18:30:54,289 - INFO - DIRECT FILTER: Medication clopidogrel not found directly, trying alternatives
2025-06-11 18:30:54,289 - INFO - DIRECT FILTER: No medication info found for clopidogrel, skipping
2025-06-11 18:30:54,290 - INFO - DIRECT FILTER: Processing DCMEDS section #15
2025-06-11 18:30:54,290 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12)
2025-06-11 18:30:54,290 - INFO - DIRECT FILTER: Medication other_p2y12 not found directly, trying alternatives
2025-06-11 18:30:54,290 - INFO - DIRECT FILTER: No medication info found for other_p2y12, skipping
2025-06-11 18:30:54,291 - INFO - DIRECT FILTER: Processing DCMEDS section #16
2025-06-11 18:30:54,291 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel)
2025-06-11 18:30:54,291 - INFO - DIRECT FILTER: Medication prasugrel not found directly, trying alternatives
2025-06-11 18:30:54,291 - INFO - DIRECT FILTER: No medication info found for prasugrel, skipping
2025-06-11 18:30:54,291 - INFO - DIRECT FILTER: Processing DCMEDS section #17
2025-06-11 18:30:54,292 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor)
2025-06-11 18:30:54,292 - INFO - DIRECT FILTER: Medication ticagrelor not found directly, trying alternatives
2025-06-11 18:30:54,292 - INFO - DIRECT FILTER: No medication info found for ticagrelor, skipping
2025-06-11 18:30:54,292 - INFO - DIRECT FILTER: Processing DCMEDS section #18
2025-06-11 18:30:54,293 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine)
2025-06-11 18:30:54,293 - INFO - DIRECT FILTER: Medication ticlopidine not found directly, trying alternatives
2025-06-11 18:30:54,293 - INFO - DIRECT FILTER: No medication info found for ticlopidine, skipping
2025-06-11 18:30:54,293 - INFO - DIRECT FILTER: Processing section ADJMEDS
2025-06-11 18:30:54,294 - INFO - DIRECT FILTER: Found 20 sections with code ADJMEDS
2025-06-11 18:30:54,294 - INFO - DIRECT FILTER: Special handling for ADJMEDS section
2025-06-11 18:30:54,294 - INFO - DIRECT FILTER: Processing ADJMEDS section #1
2025-06-11 18:30:54,294 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux)
2025-06-11 18:30:54,295 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:30:54,295 - INFO - DIRECT FILTER: Found value for fondaparinux: Yes, code: None
2025-06-11 18:30:54,295 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:30:54,297 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,298 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,298 - INFO - DIRECT FILTER: Added status element for fondaparinux with value Yes
2025-06-11 18:30:54,298 - INFO - DIRECT FILTER: Processing ADJMEDS section #2
2025-06-11 18:30:54,298 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative)
2025-06-11 18:30:54,298 - INFO - DIRECT FILTER: Found medication heparin_derivative in database
2025-06-11 18:30:54,299 - INFO - DIRECT FILTER: Found value for heparin_derivative: Yes, code: None
2025-06-11 18:30:54,299 - INFO - DIRECT FILTER: Creating status element for heparin_derivative
2025-06-11 18:30:54,300 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,301 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,301 - INFO - DIRECT FILTER: Added status element for heparin_derivative with value Yes
2025-06-11 18:30:54,302 - INFO - DIRECT FILTER: Processing ADJMEDS section #3
2025-06-11 18:30:54,302 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin)
2025-06-11 18:30:54,302 - INFO - DIRECT FILTER: Found medication low_molecular_weight_heparin in database
2025-06-11 18:30:54,302 - INFO - DIRECT FILTER: Found value for low_molecular_weight_heparin: Yes, code: None
2025-06-11 18:30:54,302 - INFO - DIRECT FILTER: Creating status element for low_molecular_weight_heparin
2025-06-11 18:30:54,304 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,305 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,305 - INFO - DIRECT FILTER: Added status element for low_molecular_weight_heparin with value Yes
2025-06-11 18:30:54,305 - INFO - DIRECT FILTER: Processing ADJMEDS section #4
2025-06-11 18:30:54,305 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin)
2025-06-11 18:30:54,306 - INFO - DIRECT FILTER: Found medication unfractionated_heparin in database
2025-06-11 18:30:54,306 - INFO - DIRECT FILTER: Found value for unfractionated_heparin: Yes, code: None
2025-06-11 18:30:54,306 - INFO - DIRECT FILTER: Creating status element for unfractionated_heparin
2025-06-11 18:30:54,308 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,308 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,309 - INFO - DIRECT FILTER: Added status element for unfractionated_heparin with value Yes
2025-06-11 18:30:54,309 - INFO - DIRECT FILTER: Processing ADJMEDS section #5
2025-06-11 18:30:54,309 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin)
2025-06-11 18:30:54,309 - INFO - DIRECT FILTER: Found medication warfarin in database
2025-06-11 18:30:54,310 - INFO - DIRECT FILTER: Found value for warfarin: No, code: None
2025-06-11 18:30:54,310 - INFO - DIRECT FILTER: Creating status element for warfarin
2025-06-11 18:30:54,311 - INFO - DIRECT FILTER: Looked up code for No: 100014173
2025-06-11 18:30:54,312 - INFO - DIRECT FILTER: Added value: 100014173 - No
2025-06-11 18:30:54,312 - INFO - DIRECT FILTER: Added status element for warfarin with value No
2025-06-11 18:30:54,313 - INFO - DIRECT FILTER: Processing ADJMEDS section #6
2025-06-11 18:30:54,313 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 81 to 100 mg (normalized: aspirin_81_100_mg)
2025-06-11 18:30:54,313 - INFO - DIRECT FILTER: Found medication aspirin_81_100_mg in database
2025-06-11 18:30:54,313 - INFO - DIRECT FILTER: Found value for aspirin_81_100_mg: Yes, code: None
2025-06-11 18:30:54,313 - INFO - DIRECT FILTER: Creating status element for aspirin_81_100_mg
2025-06-11 18:30:54,315 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,316 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,316 - INFO - DIRECT FILTER: Added status element for aspirin_81_100_mg with value Yes
2025-06-11 18:30:54,317 - INFO - DIRECT FILTER: Processing ADJMEDS section #7
2025-06-11 18:30:54,317 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 101 to 324 mg (normalized: aspirin_101_324_mg)
2025-06-11 18:30:54,317 - INFO - DIRECT FILTER: Found medication aspirin_101_324_mg in database
2025-06-11 18:30:54,317 - INFO - DIRECT FILTER: Found value for aspirin_101_324_mg: Yes, code: None
2025-06-11 18:30:54,318 - INFO - DIRECT FILTER: Creating status element for aspirin_101_324_mg
2025-06-11 18:30:54,319 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,319 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,320 - INFO - DIRECT FILTER: Added status element for aspirin_101_324_mg with value Yes
2025-06-11 18:30:54,320 - INFO - DIRECT FILTER: Processing ADJMEDS section #8
2025-06-11 18:30:54,320 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 325 mg (normalized: aspirin_325_mg)
2025-06-11 18:30:54,320 - INFO - DIRECT FILTER: Found medication aspirin_325_mg in database
2025-06-11 18:30:54,321 - INFO - DIRECT FILTER: Found value for aspirin_325_mg: Yes, code: None
2025-06-11 18:30:54,321 - INFO - DIRECT FILTER: Creating status element for aspirin_325_mg
2025-06-11 18:30:54,322 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,323 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,323 - INFO - DIRECT FILTER: Added status element for aspirin_325_mg with value Yes
2025-06-11 18:30:54,323 - INFO - DIRECT FILTER: Processing ADJMEDS section #9
2025-06-11 18:30:54,323 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole)
2025-06-11 18:30:54,323 - INFO - DIRECT FILTER: Found medication aspirin_dipyridamole in database
2025-06-11 18:30:54,324 - INFO - DIRECT FILTER: Found value for aspirin_dipyridamole: Yes, code: None
2025-06-11 18:30:54,324 - INFO - DIRECT FILTER: Creating status element for aspirin_dipyridamole
2025-06-11 18:30:54,325 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,326 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,327 - INFO - DIRECT FILTER: Added status element for aspirin_dipyridamole with value Yes
2025-06-11 18:30:54,327 - INFO - DIRECT FILTER: Processing ADJMEDS section #10
2025-06-11 18:30:54,327 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar)
2025-06-11 18:30:54,327 - INFO - DIRECT FILTER: Found medication vorapaxar in database
2025-06-11 18:30:54,328 - INFO - DIRECT FILTER: Found value for vorapaxar: Yes, code: None
2025-06-11 18:30:54,328 - INFO - DIRECT FILTER: Creating status element for vorapaxar
2025-06-11 18:30:54,329 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,330 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,330 - INFO - DIRECT FILTER: Added status element for vorapaxar with value Yes
2025-06-11 18:30:54,330 - INFO - DIRECT FILTER: Processing ADJMEDS section #11
2025-06-11 18:30:54,330 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban)
2025-06-11 18:30:54,331 - INFO - DIRECT FILTER: Found medication apixaban in database
2025-06-11 18:30:54,331 - INFO - DIRECT FILTER: Found value for apixaban: Yes, code: None
2025-06-11 18:30:54,331 - INFO - DIRECT FILTER: Creating status element for apixaban
2025-06-11 18:30:54,333 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,334 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,335 - INFO - DIRECT FILTER: Added status element for apixaban with value Yes
2025-06-11 18:30:54,335 - INFO - DIRECT FILTER: Processing ADJMEDS section #12
2025-06-11 18:30:54,335 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran)
2025-06-11 18:30:54,336 - INFO - DIRECT FILTER: Found medication dabigatran in database
2025-06-11 18:30:54,336 - INFO - DIRECT FILTER: Found value for dabigatran: Yes, code: None
2025-06-11 18:30:54,336 - INFO - DIRECT FILTER: Creating status element for dabigatran
2025-06-11 18:30:54,337 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,338 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,339 - INFO - DIRECT FILTER: Added status element for dabigatran with value Yes
2025-06-11 18:30:54,339 - INFO - DIRECT FILTER: Processing ADJMEDS section #13
2025-06-11 18:30:54,339 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban)
2025-06-11 18:30:54,339 - INFO - DIRECT FILTER: Found medication edoxaban in database
2025-06-11 18:30:54,340 - INFO - DIRECT FILTER: Found value for edoxaban: Yes, code: None
2025-06-11 18:30:54,340 - INFO - DIRECT FILTER: Creating status element for edoxaban
2025-06-11 18:30:54,341 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,342 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,342 - INFO - DIRECT FILTER: Added status element for edoxaban with value Yes
2025-06-11 18:30:54,342 - INFO - DIRECT FILTER: Processing ADJMEDS section #14
2025-06-11 18:30:54,342 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban)
2025-06-11 18:30:54,343 - INFO - DIRECT FILTER: Found medication rivaroxaban in database
2025-06-11 18:30:54,343 - INFO - DIRECT FILTER: Found value for rivaroxaban: Yes, code: None
2025-06-11 18:30:54,343 - INFO - DIRECT FILTER: Creating status element for rivaroxaban
2025-06-11 18:30:54,345 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,345 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,346 - INFO - DIRECT FILTER: Added status element for rivaroxaban with value Yes
2025-06-11 18:30:54,346 - INFO - DIRECT FILTER: Processing ADJMEDS section #15
2025-06-11 18:30:54,346 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor)
2025-06-11 18:30:54,346 - INFO - DIRECT FILTER: Found medication cangrelor in database
2025-06-11 18:30:54,347 - INFO - DIRECT FILTER: Found value for cangrelor: Yes, code: None
2025-06-11 18:30:54,347 - INFO - DIRECT FILTER: Creating status element for cangrelor
2025-06-11 18:30:54,348 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,349 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,350 - INFO - DIRECT FILTER: Added status element for cangrelor with value Yes
2025-06-11 18:30:54,350 - INFO - DIRECT FILTER: Processing ADJMEDS section #16
2025-06-11 18:30:54,350 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel)
2025-06-11 18:30:54,350 - INFO - DIRECT FILTER: Found medication clopidogrel in database
2025-06-11 18:30:54,351 - INFO - DIRECT FILTER: Found value for clopidogrel: Yes, code: None
2025-06-11 18:30:54,351 - INFO - DIRECT FILTER: Creating status element for clopidogrel
2025-06-11 18:30:54,352 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,353 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,353 - INFO - DIRECT FILTER: Added status element for clopidogrel with value Yes
2025-06-11 18:30:54,353 - INFO - DIRECT FILTER: Processing ADJMEDS section #17
2025-06-11 18:30:54,353 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12)
2025-06-11 18:30:54,354 - INFO - DIRECT FILTER: Found medication other_p2y12 in database
2025-06-11 18:30:54,354 - INFO - DIRECT FILTER: Found value for other_p2y12: Yes, code: None
2025-06-11 18:30:54,354 - INFO - DIRECT FILTER: Creating status element for other_p2y12
2025-06-11 18:30:54,355 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,356 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,356 - INFO - DIRECT FILTER: Added status element for other_p2y12 with value Yes
2025-06-11 18:30:54,357 - INFO - DIRECT FILTER: Processing ADJMEDS section #18
2025-06-11 18:30:54,357 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel)
2025-06-11 18:30:54,357 - INFO - DIRECT FILTER: Found medication prasugrel in database
2025-06-11 18:30:54,358 - INFO - DIRECT FILTER: Found value for prasugrel: Yes, code: None
2025-06-11 18:30:54,358 - INFO - DIRECT FILTER: Creating status element for prasugrel
2025-06-11 18:30:54,359 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,360 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,360 - INFO - DIRECT FILTER: Added status element for prasugrel with value Yes
2025-06-11 18:30:54,360 - INFO - DIRECT FILTER: Processing ADJMEDS section #19
2025-06-11 18:30:54,361 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor)
2025-06-11 18:30:54,361 - INFO - DIRECT FILTER: Found medication ticagrelor in database
2025-06-11 18:30:54,361 - INFO - DIRECT FILTER: Found value for ticagrelor: Yes, code: None
2025-06-11 18:30:54,362 - INFO - DIRECT FILTER: Creating status element for ticagrelor
2025-06-11 18:30:54,363 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,364 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,365 - INFO - DIRECT FILTER: Added status element for ticagrelor with value Yes
2025-06-11 18:30:54,365 - INFO - DIRECT FILTER: Processing ADJMEDS section #20
2025-06-11 18:30:54,365 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine)
2025-06-11 18:30:54,366 - INFO - DIRECT FILTER: Found medication ticlopidine in database
2025-06-11 18:30:54,366 - INFO - DIRECT FILTER: Found value for ticlopidine: Yes, code: None
2025-06-11 18:30:54,366 - INFO - DIRECT FILTER: Creating status element for ticlopidine
2025-06-11 18:30:54,368 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:30:54,368 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:30:54,369 - INFO - DIRECT FILTER: Added status element for ticlopidine with value Yes
2025-06-11 18:30:54,369 - INFO - DIRECT FILTER: Processing section FADJMEDS
2025-06-11 18:30:54,369 - INFO - DIRECT FILTER: Found 0 sections with code FADJMEDS
2025-06-11 18:30:54,369 - INFO - DIRECT FILTER: No values found for section FADJMEDS, removing status elements
