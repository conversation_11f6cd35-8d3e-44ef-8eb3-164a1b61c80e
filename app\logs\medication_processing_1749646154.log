2025-06-11 18:19:14,213 - INFO - Medication processing log started, writing to C:\Users\<USER>\Desktop\cm-api\app\logs\medication_processing_1749646154.log
2025-06-11 18:21:58,675 - INFO - TABLE FIELDS: Called with field_id=6985, key_element=medications, section={'code': 'PREPROCMED'}
2025-06-11 18:21:58,676 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:00,084 - INFO - TABLE FIELDS: Called with field_id=12153, key_element=elements, section={'code': 'IPPEVENTS'}
2025-06-11 18:22:00,084 - INFO - TABLE FIELDS: No data found for key_element=elements
2025-06-11 18:22:00,770 - INFO - TABLE FIELDS: Called with field_id=10200, key_element=medications, section={'code': 'DCMEDS'}
2025-06-11 18:22:00,772 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:01,069 - INFO - TABLE FIELDS: Called with field_id=14940, key_element=medications, section={'code': 'ADJMEDS'}
2025-06-11 18:22:01,069 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:02,729 - INFO - TABLE FIELDS: Called with field_id=11990, key_element=elements, section={'code': 'FUPMEDS'}
2025-06-11 18:22:02,729 - INFO - TABLE FIELDS: No data found for key_element=elements
2025-06-11 18:22:03,269 - INFO - TABLE FIELDS: Called with field_id=14948, key_element=event_occurred, section={'code': 'FUPEVENTS'}
2025-06-11 18:22:03,269 - INFO - TABLE FIELDS: No data found for key_element=event_occurred
2025-06-11 18:22:03,942 - INFO - TABLE FIELDS: Called with field_id=15006, key_element=medications, section={'code': 'FADJMEDS'}
2025-06-11 18:22:03,942 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:04,593 - INFO - Wrote field_result to debug file
2025-06-11 18:22:04,593 - INFO - DIRECT FILTER: Starting direct filtering of medication status elements
2025-06-11 18:22:04,605 - INFO - MEDICATION VALUES FROM DATABASE:
2025-06-11 18:22:04,605 - INFO - MEDICATION NAME MAPPINGS:
2025-06-11 18:22:04,606 - INFO -   heparin derivative -> heparin_derivative
2025-06-11 18:22:04,606 - INFO -   low molecular weight heparin -> low_molecular_weight_heparin
2025-06-11 18:22:04,606 - INFO -   unfractionated heparin -> unfractionated_heparin
2025-06-11 18:22:04,607 - INFO -   aspirin 81 to 100 mg -> aspirin_81_100_mg
2025-06-11 18:22:04,607 - INFO -   aspirin 101 to 324 mg -> aspirin_101_324_mg
2025-06-11 18:22:04,607 - INFO -   aspirin 325 mg -> aspirin_325_mg
2025-06-11 18:22:04,607 - INFO -   aspirin/dipyridamole -> aspirin_dipyridamole
2025-06-11 18:22:04,640 - INFO - Found pre_procedure_medications
2025-06-11 18:22:04,640 - INFO - Found 20 medications in pre_procedure_medications
2025-06-11 18:22:04,641 - INFO - PREPROCMED: fondaparinux
2025-06-11 18:22:04,641 - INFO -   Value: Past
2025-06-11 18:22:04,641 - INFO -   Field ID: 14883
2025-06-11 18:22:04,641 - INFO - PREPROCMED: heparin_derivative
2025-06-11 18:22:04,641 - INFO -   Value: Held
2025-06-11 18:22:04,642 - INFO -   Field ID: 14883
2025-06-11 18:22:04,642 - INFO - PREPROCMED: low_molecular_weight_heparin
2025-06-11 18:22:04,642 - INFO -   Value: Held
2025-06-11 18:22:04,642 - INFO -   Field ID: 14883
2025-06-11 18:22:04,642 - INFO - PREPROCMED: unfractionated_heparin
2025-06-11 18:22:04,643 - INFO -   Value: Held
2025-06-11 18:22:04,643 - INFO -   Field ID: 14883
2025-06-11 18:22:04,644 - INFO - PREPROCMED: warfarin
2025-06-11 18:22:04,644 - INFO -   Value: Held
2025-06-11 18:22:04,644 - INFO -   Field ID: 14883
2025-06-11 18:22:04,645 - INFO - PREPROCMED: aspirin_81_100_mg
2025-06-11 18:22:04,645 - INFO -   Value: Held
2025-06-11 18:22:04,645 - INFO -   Field ID: 14883
2025-06-11 18:22:04,645 - INFO - PREPROCMED: aspirin_101_324_mg
2025-06-11 18:22:04,646 - INFO -   Value: Held
2025-06-11 18:22:04,646 - INFO -   Field ID: 14883
2025-06-11 18:22:04,646 - INFO - PREPROCMED: aspirin_325_mg
2025-06-11 18:22:04,646 - INFO -   Value: Current
2025-06-11 18:22:04,647 - INFO -   Field ID: 14883
2025-06-11 18:22:04,647 - INFO - PREPROCMED: aspirin_dipyridamole
2025-06-11 18:22:04,647 - INFO -   Value: Held
2025-06-11 18:22:04,647 - INFO -   Field ID: 14883
2025-06-11 18:22:04,648 - INFO - PREPROCMED: vorapaxar
2025-06-11 18:22:04,648 - INFO -   Value: Past
2025-06-11 18:22:04,648 - INFO -   Field ID: 14883
2025-06-11 18:22:04,648 - INFO - PREPROCMED: apixaban
2025-06-11 18:22:04,649 - INFO -   Value: Held
2025-06-11 18:22:04,649 - INFO -   Field ID: 14883
2025-06-11 18:22:04,649 - INFO - PREPROCMED: dabigatran
2025-06-11 18:22:04,649 - INFO -   Value: Current
2025-06-11 18:22:04,649 - INFO -   Field ID: 14883
2025-06-11 18:22:04,650 - INFO - PREPROCMED: edoxaban
2025-06-11 18:22:04,650 - INFO -   Value: Held
2025-06-11 18:22:04,650 - INFO -   Field ID: 14883
2025-06-11 18:22:04,650 - INFO - PREPROCMED: rivaroxaban
2025-06-11 18:22:04,650 - INFO -   Value: Held
2025-06-11 18:22:04,651 - INFO -   Field ID: 14883
2025-06-11 18:22:04,651 - INFO - PREPROCMED: cangrelor
2025-06-11 18:22:04,651 - INFO -   Value: Past
2025-06-11 18:22:04,651 - INFO -   Field ID: 14883
2025-06-11 18:22:04,652 - INFO - PREPROCMED: clopidogrel
2025-06-11 18:22:04,652 - INFO -   Value: Current
2025-06-11 18:22:04,652 - INFO -   Field ID: 14883
2025-06-11 18:22:04,652 - INFO - PREPROCMED: other_p2y12
2025-06-11 18:22:04,653 - INFO -   Value: Held
2025-06-11 18:22:04,653 - INFO -   Field ID: 14883
2025-06-11 18:22:04,653 - INFO - PREPROCMED: prasugrel
2025-06-11 18:22:04,653 - INFO -   Value: Current
2025-06-11 18:22:04,653 - INFO -   Field ID: 14883
2025-06-11 18:22:04,654 - INFO - PREPROCMED: ticagrelor
2025-06-11 18:22:04,654 - INFO -   Value: Current
2025-06-11 18:22:04,654 - INFO -   Field ID: 14883
2025-06-11 18:22:04,654 - INFO - PREPROCMED: ticlopidine
2025-06-11 18:22:04,654 - INFO -   Value: Current
2025-06-11 18:22:04,655 - INFO -   Field ID: 14883
2025-06-11 18:22:04,655 - INFO - Found discharge_medications
2025-06-11 18:22:04,655 - INFO - Found 18 medications in discharge_medications
2025-06-11 18:22:04,655 - INFO - DCMEDS: fondaparinux
2025-06-11 18:22:04,656 - INFO -   Value: No - No Reason
2025-06-11 18:22:04,656 - INFO -   Field ID: 10205
2025-06-11 18:22:04,656 - INFO - DCMEDS: heparin_derivative
2025-06-11 18:22:04,656 - INFO -   Value: 
2025-06-11 18:22:04,656 - INFO -   Field ID: 10205
2025-06-11 18:22:04,657 - INFO - DCMEDS: low_molecular_weight_heparin
2025-06-11 18:22:04,657 - INFO -   Value: 
2025-06-11 18:22:04,657 - INFO -   Field ID: 10205
2025-06-11 18:22:04,657 - INFO - DCMEDS: unfractionated_heparin
2025-06-11 18:22:04,657 - INFO -   Value: 
2025-06-11 18:22:04,658 - INFO -   Field ID: 10205
2025-06-11 18:22:04,658 - INFO - DCMEDS: warfarin
2025-06-11 18:22:04,658 - INFO -   Value: 
2025-06-11 18:22:04,658 - INFO -   Field ID: 10205
2025-06-11 18:22:04,658 - INFO - DCMEDS: aspirin
2025-06-11 18:22:04,659 - INFO -   Value: Yes
2025-06-11 18:22:04,659 - INFO -   Field ID: 10205
2025-06-11 18:22:04,659 - INFO -   Found if_yes for aspirin
2025-06-11 18:22:04,659 - INFO -   Aspirin dose: 81 - 100 MG
2025-06-11 18:22:04,660 - INFO -   Dose field ID: 10207
2025-06-11 18:22:04,660 - INFO -   Added aspirin_81_100_mg with value Yes
2025-06-11 18:22:04,660 - INFO - DCMEDS: aspirin_dipyridamole
2025-06-11 18:22:04,660 - INFO -   Value: 
2025-06-11 18:22:04,661 - INFO -   Field ID: 10205
2025-06-11 18:22:04,661 - INFO - DCMEDS: vorapaxar
2025-06-11 18:22:04,661 - INFO -   Value: 
2025-06-11 18:22:04,662 - INFO -   Field ID: 10205
2025-06-11 18:22:04,662 - INFO - DCMEDS: apixaban
2025-06-11 18:22:04,662 - INFO -   Value: 
2025-06-11 18:22:04,662 - INFO -   Field ID: 10205
2025-06-11 18:22:04,662 - INFO - DCMEDS: dabigatran
2025-06-11 18:22:04,663 - INFO -   Value: 
2025-06-11 18:22:04,663 - INFO -   Field ID: 10205
2025-06-11 18:22:04,663 - INFO - DCMEDS: edoxaban
2025-06-11 18:22:04,663 - INFO -   Value: 
2025-06-11 18:22:04,663 - INFO -   Field ID: 10205
2025-06-11 18:22:04,664 - INFO - DCMEDS: rivaroxaban
2025-06-11 18:22:04,664 - INFO -   Value: 
2025-06-11 18:22:04,664 - INFO -   Field ID: 10205
2025-06-11 18:22:04,665 - INFO - DCMEDS: cangrelor
2025-06-11 18:22:04,665 - INFO -   Value: 
2025-06-11 18:22:04,665 - INFO -   Field ID: 10205
2025-06-11 18:22:04,665 - INFO - DCMEDS: clopidogrel
2025-06-11 18:22:04,666 - INFO -   Value: 
2025-06-11 18:22:04,666 - INFO -   Field ID: 10205
2025-06-11 18:22:04,666 - INFO - DCMEDS: other_p2y12
2025-06-11 18:22:04,666 - INFO -   Value: 
2025-06-11 18:22:04,667 - INFO -   Field ID: 10205
2025-06-11 18:22:04,667 - INFO - DCMEDS: prasugrel
2025-06-11 18:22:04,667 - INFO -   Value: 
2025-06-11 18:22:04,667 - INFO -   Field ID: 10205
2025-06-11 18:22:04,667 - INFO - DCMEDS: ticagrelor
2025-06-11 18:22:04,668 - INFO -   Value: 
2025-06-11 18:22:04,668 - INFO -   Field ID: 10205
2025-06-11 18:22:04,668 - INFO - DCMEDS: ticlopidine
2025-06-11 18:22:04,668 - INFO -   Value: 
2025-06-11 18:22:04,668 - INFO -   Field ID: 10205
2025-06-11 18:22:04,669 - INFO - Found in_hospital_adjudication
2025-06-11 18:22:04,669 - INFO - Found 20 medications in in_hospital_adjudication
2025-06-11 18:22:04,669 - INFO - ADJMEDS: fondaparinux
2025-06-11 18:22:04,669 - INFO -   Value: Yes
2025-06-11 18:22:04,670 - INFO -   Field ID: 14941
2025-06-11 18:22:04,670 - INFO - ADJMEDS: heparin_derivative
2025-06-11 18:22:04,670 - INFO -   Value: Yes
2025-06-11 18:22:04,670 - INFO -   Field ID: 14941
2025-06-11 18:22:04,670 - INFO - ADJMEDS: low_molecular_weight_heparin
2025-06-11 18:22:04,671 - INFO -   Value: Yes
2025-06-11 18:22:04,671 - INFO -   Field ID: 14941
2025-06-11 18:22:04,671 - INFO - ADJMEDS: unfractionated_heparin
2025-06-11 18:22:04,671 - INFO -   Value: Yes
2025-06-11 18:22:04,672 - INFO -   Field ID: 14941
2025-06-11 18:22:04,672 - INFO - ADJMEDS: warfarin
2025-06-11 18:22:04,672 - INFO -   Value: No
2025-06-11 18:22:04,672 - INFO -   Field ID: 14941
2025-06-11 18:22:04,673 - INFO - ADJMEDS: aspirin_81_100_mg
2025-06-11 18:22:04,673 - INFO -   Value: Yes
2025-06-11 18:22:04,673 - INFO -   Field ID: 14941
2025-06-11 18:22:04,674 - INFO - ADJMEDS: aspirin_101_324_mg
2025-06-11 18:22:04,674 - INFO -   Value: Yes
2025-06-11 18:22:04,674 - INFO -   Field ID: 14941
2025-06-11 18:22:04,674 - INFO - ADJMEDS: aspirin_325_mg
2025-06-11 18:22:04,675 - INFO -   Value: Yes
2025-06-11 18:22:04,675 - INFO -   Field ID: 14941
2025-06-11 18:22:04,675 - INFO - ADJMEDS: aspirin_dipyridamole
2025-06-11 18:22:04,675 - INFO -   Value: Yes
2025-06-11 18:22:04,676 - INFO -   Field ID: 14941
2025-06-11 18:22:04,676 - INFO - ADJMEDS: vorapaxar
2025-06-11 18:22:04,676 - INFO -   Value: Yes
2025-06-11 18:22:04,676 - INFO -   Field ID: 14941
2025-06-11 18:22:04,677 - INFO - ADJMEDS: apixaban
2025-06-11 18:22:04,677 - INFO -   Value: Yes
2025-06-11 18:22:04,677 - INFO -   Field ID: 14941
2025-06-11 18:22:04,677 - INFO - ADJMEDS: dabigatran
2025-06-11 18:22:04,677 - INFO -   Value: Yes
2025-06-11 18:22:04,678 - INFO -   Field ID: 14941
2025-06-11 18:22:04,678 - INFO - ADJMEDS: edoxaban
2025-06-11 18:22:04,678 - INFO -   Value: Yes
2025-06-11 18:22:04,678 - INFO -   Field ID: 14941
2025-06-11 18:22:04,678 - INFO - ADJMEDS: rivaroxaban
2025-06-11 18:22:04,679 - INFO -   Value: Yes
2025-06-11 18:22:04,679 - INFO -   Field ID: 14941
2025-06-11 18:22:04,679 - INFO - ADJMEDS: cangrelor
2025-06-11 18:22:04,679 - INFO -   Value: Yes
2025-06-11 18:22:04,680 - INFO -   Field ID: 14941
2025-06-11 18:22:04,680 - INFO - ADJMEDS: clopidogrel
2025-06-11 18:22:04,680 - INFO -   Value: Yes
2025-06-11 18:22:04,680 - INFO -   Field ID: 14941
2025-06-11 18:22:04,680 - INFO - ADJMEDS: other_p2y12
2025-06-11 18:22:04,681 - INFO -   Value: Yes
2025-06-11 18:22:04,681 - INFO -   Field ID: 14941
2025-06-11 18:22:04,681 - INFO - ADJMEDS: prasugrel
2025-06-11 18:22:04,681 - INFO -   Value: Yes
2025-06-11 18:22:04,682 - INFO -   Field ID: 14941
2025-06-11 18:22:04,682 - INFO - ADJMEDS: ticagrelor
2025-06-11 18:22:04,682 - INFO -   Value: Yes
2025-06-11 18:22:04,682 - INFO -   Field ID: 14941
2025-06-11 18:22:04,683 - INFO - ADJMEDS: ticlopidine
2025-06-11 18:22:04,683 - INFO -   Value: Yes
2025-06-11 18:22:04,683 - INFO -   Field ID: 14941
2025-06-11 18:22:04,683 - INFO - DIRECT FILTER: Processing section PREPROCMED
2025-06-11 18:22:04,684 - INFO - DIRECT FILTER: Found 20 sections with code PREPROCMED
2025-06-11 18:22:04,684 - INFO - DIRECT FILTER: Processing section PREPROCMED #1
2025-06-11 18:22:04,684 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #1
2025-06-11 18:22:04,685 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux) in section PREPROCMED #1
2025-06-11 18:22:04,685 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:22:04,685 - INFO - DIRECT FILTER: Found value for fondaparinux: Past, code: None
2025-06-11 18:22:04,685 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:22:04,687 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:22:04,688 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:22:04,689 - INFO - DIRECT FILTER: Added status element for fondaparinux with value Past in section PREPROCMED #1
2025-06-11 18:22:04,689 - INFO - DIRECT FILTER: Processing section PREPROCMED #2
2025-06-11 18:22:04,690 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #2
2025-06-11 18:22:04,690 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative) in section PREPROCMED #2
2025-06-11 18:22:04,690 - INFO - DIRECT FILTER: Found medication heparin_derivative in database
2025-06-11 18:22:04,690 - INFO - DIRECT FILTER: Found value for heparin_derivative: Held, code: None
2025-06-11 18:22:04,691 - INFO - DIRECT FILTER: Creating status element for heparin_derivative
2025-06-11 18:22:04,692 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,694 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,694 - INFO - DIRECT FILTER: Added status element for heparin_derivative with value Held in section PREPROCMED #2
2025-06-11 18:22:04,694 - INFO - DIRECT FILTER: Processing section PREPROCMED #3
2025-06-11 18:22:04,695 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #3
2025-06-11 18:22:04,695 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin) in section PREPROCMED #3
2025-06-11 18:22:04,695 - INFO - DIRECT FILTER: Found medication low_molecular_weight_heparin in database
2025-06-11 18:22:04,695 - INFO - DIRECT FILTER: Found value for low_molecular_weight_heparin: Held, code: None
2025-06-11 18:22:04,696 - INFO - DIRECT FILTER: Creating status element for low_molecular_weight_heparin
2025-06-11 18:22:04,698 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,699 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,699 - INFO - DIRECT FILTER: Added status element for low_molecular_weight_heparin with value Held in section PREPROCMED #3
2025-06-11 18:22:04,699 - INFO - DIRECT FILTER: Processing section PREPROCMED #4
2025-06-11 18:22:04,700 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #4
2025-06-11 18:22:04,700 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin) in section PREPROCMED #4
2025-06-11 18:22:04,700 - INFO - DIRECT FILTER: Found medication unfractionated_heparin in database
2025-06-11 18:22:04,701 - INFO - DIRECT FILTER: Found value for unfractionated_heparin: Held, code: None
2025-06-11 18:22:04,702 - INFO - DIRECT FILTER: Creating status element for unfractionated_heparin
2025-06-11 18:22:04,704 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,705 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,705 - INFO - DIRECT FILTER: Added status element for unfractionated_heparin with value Held in section PREPROCMED #4
2025-06-11 18:22:04,705 - INFO - DIRECT FILTER: Processing section PREPROCMED #5
2025-06-11 18:22:04,706 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #5
2025-06-11 18:22:04,706 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin) in section PREPROCMED #5
2025-06-11 18:22:04,706 - INFO - DIRECT FILTER: Found medication warfarin in database
2025-06-11 18:22:04,707 - INFO - DIRECT FILTER: Found value for warfarin: Held, code: None
2025-06-11 18:22:04,707 - INFO - DIRECT FILTER: Creating status element for warfarin
2025-06-11 18:22:04,708 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,710 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,710 - INFO - DIRECT FILTER: Added status element for warfarin with value Held in section PREPROCMED #5
2025-06-11 18:22:04,711 - INFO - DIRECT FILTER: Processing section PREPROCMED #6
2025-06-11 18:22:04,711 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #6
2025-06-11 18:22:04,712 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 81 to 100 mg (normalized: aspirin_81_100_mg) in section PREPROCMED #6
2025-06-11 18:22:04,712 - INFO - DIRECT FILTER: Found medication aspirin_81_100_mg in database
2025-06-11 18:22:04,712 - INFO - DIRECT FILTER: Found value for aspirin_81_100_mg: Held, code: None
2025-06-11 18:22:04,713 - INFO - DIRECT FILTER: Creating status element for aspirin_81_100_mg
2025-06-11 18:22:04,714 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,715 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,716 - INFO - DIRECT FILTER: Added status element for aspirin_81_100_mg with value Held in section PREPROCMED #6
2025-06-11 18:22:04,716 - INFO - DIRECT FILTER: Processing section PREPROCMED #7
2025-06-11 18:22:04,717 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #7
2025-06-11 18:22:04,717 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 101 to 324 mg (normalized: aspirin_101_324_mg) in section PREPROCMED #7
2025-06-11 18:22:04,717 - INFO - DIRECT FILTER: Found medication aspirin_101_324_mg in database
2025-06-11 18:22:04,717 - INFO - DIRECT FILTER: Found value for aspirin_101_324_mg: Held, code: None
2025-06-11 18:22:04,718 - INFO - DIRECT FILTER: Creating status element for aspirin_101_324_mg
2025-06-11 18:22:04,720 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,721 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,721 - INFO - DIRECT FILTER: Added status element for aspirin_101_324_mg with value Held in section PREPROCMED #7
2025-06-11 18:22:04,722 - INFO - DIRECT FILTER: Processing section PREPROCMED #8
2025-06-11 18:22:04,722 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #8
2025-06-11 18:22:04,723 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 325 mg (normalized: aspirin_325_mg) in section PREPROCMED #8
2025-06-11 18:22:04,723 - INFO - DIRECT FILTER: Found medication aspirin_325_mg in database
2025-06-11 18:22:04,723 - INFO - DIRECT FILTER: Found value for aspirin_325_mg: Current, code: None
2025-06-11 18:22:04,723 - INFO - DIRECT FILTER: Creating status element for aspirin_325_mg
2025-06-11 18:22:04,725 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:04,726 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:04,726 - INFO - DIRECT FILTER: Added status element for aspirin_325_mg with value Current in section PREPROCMED #8
2025-06-11 18:22:04,727 - INFO - DIRECT FILTER: Processing section PREPROCMED #9
2025-06-11 18:22:04,727 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #9
2025-06-11 18:22:04,727 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole) in section PREPROCMED #9
2025-06-11 18:22:04,727 - INFO - DIRECT FILTER: Found medication aspirin_dipyridamole in database
2025-06-11 18:22:04,728 - INFO - DIRECT FILTER: Found value for aspirin_dipyridamole: Held, code: None
2025-06-11 18:22:04,728 - INFO - DIRECT FILTER: Creating status element for aspirin_dipyridamole
2025-06-11 18:22:04,730 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,730 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,731 - INFO - DIRECT FILTER: Added status element for aspirin_dipyridamole with value Held in section PREPROCMED #9
2025-06-11 18:22:04,731 - INFO - DIRECT FILTER: Processing section PREPROCMED #10
2025-06-11 18:22:04,731 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #10
2025-06-11 18:22:04,732 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar) in section PREPROCMED #10
2025-06-11 18:22:04,732 - INFO - DIRECT FILTER: Found medication vorapaxar in database
2025-06-11 18:22:04,732 - INFO - DIRECT FILTER: Found value for vorapaxar: Past, code: None
2025-06-11 18:22:04,732 - INFO - DIRECT FILTER: Creating status element for vorapaxar
2025-06-11 18:22:04,734 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:22:04,735 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:22:04,736 - INFO - DIRECT FILTER: Added status element for vorapaxar with value Past in section PREPROCMED #10
2025-06-11 18:22:04,736 - INFO - DIRECT FILTER: Processing section PREPROCMED #11
2025-06-11 18:22:04,736 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #11
2025-06-11 18:22:04,736 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban) in section PREPROCMED #11
2025-06-11 18:22:04,737 - INFO - DIRECT FILTER: Found medication apixaban in database
2025-06-11 18:22:04,737 - INFO - DIRECT FILTER: Found value for apixaban: Held, code: None
2025-06-11 18:22:04,737 - INFO - DIRECT FILTER: Creating status element for apixaban
2025-06-11 18:22:04,738 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,740 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,740 - INFO - DIRECT FILTER: Added status element for apixaban with value Held in section PREPROCMED #11
2025-06-11 18:22:04,740 - INFO - DIRECT FILTER: Processing section PREPROCMED #12
2025-06-11 18:22:04,741 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #12
2025-06-11 18:22:04,741 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran) in section PREPROCMED #12
2025-06-11 18:22:04,741 - INFO - DIRECT FILTER: Found medication dabigatran in database
2025-06-11 18:22:04,741 - INFO - DIRECT FILTER: Found value for dabigatran: Current, code: None
2025-06-11 18:22:04,742 - INFO - DIRECT FILTER: Creating status element for dabigatran
2025-06-11 18:22:04,744 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:04,745 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:04,745 - INFO - DIRECT FILTER: Added status element for dabigatran with value Current in section PREPROCMED #12
2025-06-11 18:22:04,746 - INFO - DIRECT FILTER: Processing section PREPROCMED #13
2025-06-11 18:22:04,746 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #13
2025-06-11 18:22:04,746 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban) in section PREPROCMED #13
2025-06-11 18:22:04,746 - INFO - DIRECT FILTER: Found medication edoxaban in database
2025-06-11 18:22:04,747 - INFO - DIRECT FILTER: Found value for edoxaban: Held, code: None
2025-06-11 18:22:04,747 - INFO - DIRECT FILTER: Creating status element for edoxaban
2025-06-11 18:22:04,749 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,750 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,750 - INFO - DIRECT FILTER: Added status element for edoxaban with value Held in section PREPROCMED #13
2025-06-11 18:22:04,751 - INFO - DIRECT FILTER: Processing section PREPROCMED #14
2025-06-11 18:22:04,751 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #14
2025-06-11 18:22:04,751 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban) in section PREPROCMED #14
2025-06-11 18:22:04,752 - INFO - DIRECT FILTER: Found medication rivaroxaban in database
2025-06-11 18:22:04,752 - INFO - DIRECT FILTER: Found value for rivaroxaban: Held, code: None
2025-06-11 18:22:04,752 - INFO - DIRECT FILTER: Creating status element for rivaroxaban
2025-06-11 18:22:04,754 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,754 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,755 - INFO - DIRECT FILTER: Added status element for rivaroxaban with value Held in section PREPROCMED #14
2025-06-11 18:22:04,755 - INFO - DIRECT FILTER: Processing section PREPROCMED #15
2025-06-11 18:22:04,755 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #15
2025-06-11 18:22:04,756 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor) in section PREPROCMED #15
2025-06-11 18:22:04,756 - INFO - DIRECT FILTER: Found medication cangrelor in database
2025-06-11 18:22:04,756 - INFO - DIRECT FILTER: Found value for cangrelor: Past, code: None
2025-06-11 18:22:04,757 - INFO - DIRECT FILTER: Creating status element for cangrelor
2025-06-11 18:22:04,759 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:22:04,760 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:22:04,760 - INFO - DIRECT FILTER: Added status element for cangrelor with value Past in section PREPROCMED #15
2025-06-11 18:22:04,760 - INFO - DIRECT FILTER: Processing section PREPROCMED #16
2025-06-11 18:22:04,761 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #16
2025-06-11 18:22:04,761 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel) in section PREPROCMED #16
2025-06-11 18:22:04,762 - INFO - DIRECT FILTER: Found medication clopidogrel in database
2025-06-11 18:22:04,762 - INFO - DIRECT FILTER: Found value for clopidogrel: Current, code: None
2025-06-11 18:22:04,762 - INFO - DIRECT FILTER: Creating status element for clopidogrel
2025-06-11 18:22:04,764 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:04,765 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:04,766 - INFO - DIRECT FILTER: Added status element for clopidogrel with value Current in section PREPROCMED #16
2025-06-11 18:22:04,766 - INFO - DIRECT FILTER: Processing section PREPROCMED #17
2025-06-11 18:22:04,767 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #17
2025-06-11 18:22:04,767 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12) in section PREPROCMED #17
2025-06-11 18:22:04,767 - INFO - DIRECT FILTER: Found medication other_p2y12 in database
2025-06-11 18:22:04,768 - INFO - DIRECT FILTER: Found value for other_p2y12: Held, code: None
2025-06-11 18:22:04,768 - INFO - DIRECT FILTER: Creating status element for other_p2y12
2025-06-11 18:22:04,770 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:04,771 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:04,771 - INFO - DIRECT FILTER: Added status element for other_p2y12 with value Held in section PREPROCMED #17
2025-06-11 18:22:04,771 - INFO - DIRECT FILTER: Processing section PREPROCMED #18
2025-06-11 18:22:04,771 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #18
2025-06-11 18:22:04,772 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel) in section PREPROCMED #18
2025-06-11 18:22:04,772 - INFO - DIRECT FILTER: Found medication prasugrel in database
2025-06-11 18:22:04,772 - INFO - DIRECT FILTER: Found value for prasugrel: Current, code: None
2025-06-11 18:22:04,773 - INFO - DIRECT FILTER: Creating status element for prasugrel
2025-06-11 18:22:04,775 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:04,775 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:04,776 - INFO - DIRECT FILTER: Added status element for prasugrel with value Current in section PREPROCMED #18
2025-06-11 18:22:04,776 - INFO - DIRECT FILTER: Processing section PREPROCMED #19
2025-06-11 18:22:04,776 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #19
2025-06-11 18:22:04,777 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor) in section PREPROCMED #19
2025-06-11 18:22:04,777 - INFO - DIRECT FILTER: Found medication ticagrelor in database
2025-06-11 18:22:04,777 - INFO - DIRECT FILTER: Found value for ticagrelor: Current, code: None
2025-06-11 18:22:04,778 - INFO - DIRECT FILTER: Creating status element for ticagrelor
2025-06-11 18:22:04,779 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:04,780 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:04,781 - INFO - DIRECT FILTER: Added status element for ticagrelor with value Current in section PREPROCMED #19
2025-06-11 18:22:04,781 - INFO - DIRECT FILTER: Processing section PREPROCMED #20
2025-06-11 18:22:04,781 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #20
2025-06-11 18:22:04,782 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine) in section PREPROCMED #20
2025-06-11 18:22:04,782 - INFO - DIRECT FILTER: Found medication ticlopidine in database
2025-06-11 18:22:04,783 - INFO - DIRECT FILTER: Found value for ticlopidine: Current, code: None
2025-06-11 18:22:04,783 - INFO - DIRECT FILTER: Creating status element for ticlopidine
2025-06-11 18:22:04,785 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:04,786 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:04,787 - INFO - DIRECT FILTER: Added status element for ticlopidine with value Current in section PREPROCMED #20
2025-06-11 18:22:04,787 - INFO - DIRECT FILTER: Processing section DCMEDS
2025-06-11 18:22:04,788 - INFO - DIRECT FILTER: Found 18 sections with code DCMEDS
2025-06-11 18:22:04,788 - INFO - DIRECT FILTER: Special handling for DCMEDS section
2025-06-11 18:22:04,788 - INFO - DIRECT FILTER: Removing status element with displayName 'Discharge Medication Dose' from DCMEDS
2025-06-11 18:22:04,789 - INFO - DIRECT FILTER: Processing DCMEDS section #1
2025-06-11 18:22:04,790 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux)
2025-06-11 18:22:04,790 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:22:04,790 - INFO - DIRECT FILTER: Found value for fondaparinux: No - No Reason, code: None
2025-06-11 18:22:04,791 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:22:04,792 - INFO - DIRECT FILTER: Looked up code for No - No Reason: 100001048
2025-06-11 18:22:04,793 - INFO - DIRECT FILTER: Added value: 100001048 - No - No Reason
2025-06-11 18:22:04,793 - INFO - DIRECT FILTER: Added status element for fondaparinux with value No - No Reason
2025-06-11 18:22:04,794 - INFO - DIRECT FILTER: Processing DCMEDS section #2
2025-06-11 18:22:04,794 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative)
2025-06-11 18:22:04,794 - INFO - DIRECT FILTER: Medication heparin_derivative not found directly, trying alternatives
2025-06-11 18:22:04,795 - INFO - DIRECT FILTER: No medication info found for heparin_derivative, skipping
2025-06-11 18:22:04,796 - INFO - DIRECT FILTER: Processing DCMEDS section #3
2025-06-11 18:22:04,797 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin)
2025-06-11 18:22:04,798 - INFO - DIRECT FILTER: Medication low_molecular_weight_heparin not found directly, trying alternatives
2025-06-11 18:22:04,798 - INFO - DIRECT FILTER: No medication info found for low_molecular_weight_heparin, skipping
2025-06-11 18:22:04,799 - INFO - DIRECT FILTER: Processing DCMEDS section #4
2025-06-11 18:22:04,799 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin)
2025-06-11 18:22:04,799 - INFO - DIRECT FILTER: Medication unfractionated_heparin not found directly, trying alternatives
2025-06-11 18:22:04,800 - INFO - DIRECT FILTER: No medication info found for unfractionated_heparin, skipping
2025-06-11 18:22:04,800 - INFO - DIRECT FILTER: Processing DCMEDS section #5
2025-06-11 18:22:04,800 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin)
2025-06-11 18:22:04,801 - INFO - DIRECT FILTER: Medication warfarin not found directly, trying alternatives
2025-06-11 18:22:04,801 - INFO - DIRECT FILTER: No medication info found for warfarin, skipping
2025-06-11 18:22:04,801 - INFO - DIRECT FILTER: Processing DCMEDS section #6
2025-06-11 18:22:04,801 - INFO - DIRECT FILTER: Processing medication #1: Aspirin (normalized: aspirin)
2025-06-11 18:22:04,802 - INFO - DIRECT FILTER: Found medication aspirin in database
2025-06-11 18:22:04,802 - INFO - DIRECT FILTER: Found value for aspirin: Yes, code: None
2025-06-11 18:22:04,802 - INFO - DIRECT FILTER: Creating status element for aspirin
2025-06-11 18:22:04,804 - INFO - DIRECT FILTER: Looked up code for Yes: 100001247
2025-06-11 18:22:04,806 - INFO - DIRECT FILTER: Added value: 100001247 - Yes
2025-06-11 18:22:04,806 - INFO - DIRECT FILTER: Added status element for aspirin with value Yes
2025-06-11 18:22:04,807 - INFO - DIRECT FILTER: Processing DCMEDS section #7
2025-06-11 18:22:04,807 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole)
2025-06-11 18:22:04,807 - INFO - DIRECT FILTER: Medication aspirin_dipyridamole not found directly, trying alternatives
2025-06-11 18:22:04,808 - INFO - DIRECT FILTER: No medication info found for aspirin_dipyridamole, skipping
2025-06-11 18:22:04,808 - INFO - DIRECT FILTER: Processing DCMEDS section #8
2025-06-11 18:22:04,808 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar)
2025-06-11 18:22:04,808 - INFO - DIRECT FILTER: Medication vorapaxar not found directly, trying alternatives
2025-06-11 18:22:04,809 - INFO - DIRECT FILTER: No medication info found for vorapaxar, skipping
2025-06-11 18:22:04,809 - INFO - DIRECT FILTER: Processing DCMEDS section #9
2025-06-11 18:22:04,809 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban)
2025-06-11 18:22:04,810 - INFO - DIRECT FILTER: Medication apixaban not found directly, trying alternatives
2025-06-11 18:22:04,810 - INFO - DIRECT FILTER: No medication info found for apixaban, skipping
2025-06-11 18:22:04,810 - INFO - DIRECT FILTER: Processing DCMEDS section #10
2025-06-11 18:22:04,810 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran)
2025-06-11 18:22:04,811 - INFO - DIRECT FILTER: Medication dabigatran not found directly, trying alternatives
2025-06-11 18:22:04,811 - INFO - DIRECT FILTER: No medication info found for dabigatran, skipping
2025-06-11 18:22:04,811 - INFO - DIRECT FILTER: Processing DCMEDS section #11
2025-06-11 18:22:04,811 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban)
2025-06-11 18:22:04,812 - INFO - DIRECT FILTER: Medication edoxaban not found directly, trying alternatives
2025-06-11 18:22:04,812 - INFO - DIRECT FILTER: No medication info found for edoxaban, skipping
2025-06-11 18:22:04,812 - INFO - DIRECT FILTER: Processing DCMEDS section #12
2025-06-11 18:22:04,813 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban)
2025-06-11 18:22:04,813 - INFO - DIRECT FILTER: Medication rivaroxaban not found directly, trying alternatives
2025-06-11 18:22:04,813 - INFO - DIRECT FILTER: No medication info found for rivaroxaban, skipping
2025-06-11 18:22:04,813 - INFO - DIRECT FILTER: Processing DCMEDS section #13
2025-06-11 18:22:04,814 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor)
2025-06-11 18:22:04,814 - INFO - DIRECT FILTER: Medication cangrelor not found directly, trying alternatives
2025-06-11 18:22:04,814 - INFO - DIRECT FILTER: No medication info found for cangrelor, skipping
2025-06-11 18:22:04,815 - INFO - DIRECT FILTER: Processing DCMEDS section #14
2025-06-11 18:22:04,815 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel)
2025-06-11 18:22:04,815 - INFO - DIRECT FILTER: Medication clopidogrel not found directly, trying alternatives
2025-06-11 18:22:04,816 - INFO - DIRECT FILTER: No medication info found for clopidogrel, skipping
2025-06-11 18:22:04,816 - INFO - DIRECT FILTER: Processing DCMEDS section #15
2025-06-11 18:22:04,816 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12)
2025-06-11 18:22:04,816 - INFO - DIRECT FILTER: Medication other_p2y12 not found directly, trying alternatives
2025-06-11 18:22:04,817 - INFO - DIRECT FILTER: No medication info found for other_p2y12, skipping
2025-06-11 18:22:04,817 - INFO - DIRECT FILTER: Processing DCMEDS section #16
2025-06-11 18:22:04,817 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel)
2025-06-11 18:22:04,818 - INFO - DIRECT FILTER: Medication prasugrel not found directly, trying alternatives
2025-06-11 18:22:04,818 - INFO - DIRECT FILTER: No medication info found for prasugrel, skipping
2025-06-11 18:22:04,818 - INFO - DIRECT FILTER: Processing DCMEDS section #17
2025-06-11 18:22:04,818 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor)
2025-06-11 18:22:04,819 - INFO - DIRECT FILTER: Medication ticagrelor not found directly, trying alternatives
2025-06-11 18:22:04,819 - INFO - DIRECT FILTER: No medication info found for ticagrelor, skipping
2025-06-11 18:22:04,819 - INFO - DIRECT FILTER: Processing DCMEDS section #18
2025-06-11 18:22:04,819 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine)
2025-06-11 18:22:04,820 - INFO - DIRECT FILTER: Medication ticlopidine not found directly, trying alternatives
2025-06-11 18:22:04,820 - INFO - DIRECT FILTER: No medication info found for ticlopidine, skipping
2025-06-11 18:22:04,820 - INFO - DIRECT FILTER: Processing section ADJMEDS
2025-06-11 18:22:04,821 - INFO - DIRECT FILTER: Found 20 sections with code ADJMEDS
2025-06-11 18:22:04,821 - INFO - DIRECT FILTER: Special handling for ADJMEDS section
2025-06-11 18:22:04,821 - INFO - DIRECT FILTER: Processing ADJMEDS section #1
2025-06-11 18:22:04,822 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux)
2025-06-11 18:22:04,822 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:22:04,822 - INFO - DIRECT FILTER: Found value for fondaparinux: Yes, code: None
2025-06-11 18:22:04,822 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:22:04,825 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,825 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,826 - INFO - DIRECT FILTER: Added status element for fondaparinux with value Yes
2025-06-11 18:22:04,826 - INFO - DIRECT FILTER: Processing ADJMEDS section #2
2025-06-11 18:22:04,826 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative)
2025-06-11 18:22:04,826 - INFO - DIRECT FILTER: Found medication heparin_derivative in database
2025-06-11 18:22:04,827 - INFO - DIRECT FILTER: Found value for heparin_derivative: Yes, code: None
2025-06-11 18:22:04,827 - INFO - DIRECT FILTER: Creating status element for heparin_derivative
2025-06-11 18:22:04,829 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,830 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,830 - INFO - DIRECT FILTER: Added status element for heparin_derivative with value Yes
2025-06-11 18:22:04,830 - INFO - DIRECT FILTER: Processing ADJMEDS section #3
2025-06-11 18:22:04,831 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin)
2025-06-11 18:22:04,831 - INFO - DIRECT FILTER: Found medication low_molecular_weight_heparin in database
2025-06-11 18:22:04,831 - INFO - DIRECT FILTER: Found value for low_molecular_weight_heparin: Yes, code: None
2025-06-11 18:22:04,832 - INFO - DIRECT FILTER: Creating status element for low_molecular_weight_heparin
2025-06-11 18:22:04,833 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,834 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,835 - INFO - DIRECT FILTER: Added status element for low_molecular_weight_heparin with value Yes
2025-06-11 18:22:04,835 - INFO - DIRECT FILTER: Processing ADJMEDS section #4
2025-06-11 18:22:04,835 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin)
2025-06-11 18:22:04,835 - INFO - DIRECT FILTER: Found medication unfractionated_heparin in database
2025-06-11 18:22:04,836 - INFO - DIRECT FILTER: Found value for unfractionated_heparin: Yes, code: None
2025-06-11 18:22:04,836 - INFO - DIRECT FILTER: Creating status element for unfractionated_heparin
2025-06-11 18:22:04,838 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,839 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,839 - INFO - DIRECT FILTER: Added status element for unfractionated_heparin with value Yes
2025-06-11 18:22:04,839 - INFO - DIRECT FILTER: Processing ADJMEDS section #5
2025-06-11 18:22:04,840 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin)
2025-06-11 18:22:04,840 - INFO - DIRECT FILTER: Found medication warfarin in database
2025-06-11 18:22:04,840 - INFO - DIRECT FILTER: Found value for warfarin: No, code: None
2025-06-11 18:22:04,841 - INFO - DIRECT FILTER: Creating status element for warfarin
2025-06-11 18:22:04,842 - INFO - DIRECT FILTER: Looked up code for No: 100014173
2025-06-11 18:22:04,843 - INFO - DIRECT FILTER: Added value: 100014173 - No
2025-06-11 18:22:04,843 - INFO - DIRECT FILTER: Added status element for warfarin with value No
2025-06-11 18:22:04,844 - INFO - DIRECT FILTER: Processing ADJMEDS section #6
2025-06-11 18:22:04,845 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 81 to 100 mg (normalized: aspirin_81_100_mg)
2025-06-11 18:22:04,845 - INFO - DIRECT FILTER: Found medication aspirin_81_100_mg in database
2025-06-11 18:22:04,846 - INFO - DIRECT FILTER: Found value for aspirin_81_100_mg: Yes, code: None
2025-06-11 18:22:04,846 - INFO - DIRECT FILTER: Creating status element for aspirin_81_100_mg
2025-06-11 18:22:04,848 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,849 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,850 - INFO - DIRECT FILTER: Added status element for aspirin_81_100_mg with value Yes
2025-06-11 18:22:04,850 - INFO - DIRECT FILTER: Processing ADJMEDS section #7
2025-06-11 18:22:04,850 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 101 to 324 mg (normalized: aspirin_101_324_mg)
2025-06-11 18:22:04,851 - INFO - DIRECT FILTER: Found medication aspirin_101_324_mg in database
2025-06-11 18:22:04,851 - INFO - DIRECT FILTER: Found value for aspirin_101_324_mg: Yes, code: None
2025-06-11 18:22:04,851 - INFO - DIRECT FILTER: Creating status element for aspirin_101_324_mg
2025-06-11 18:22:04,853 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,854 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,854 - INFO - DIRECT FILTER: Added status element for aspirin_101_324_mg with value Yes
2025-06-11 18:22:04,855 - INFO - DIRECT FILTER: Processing ADJMEDS section #8
2025-06-11 18:22:04,855 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 325 mg (normalized: aspirin_325_mg)
2025-06-11 18:22:04,855 - INFO - DIRECT FILTER: Found medication aspirin_325_mg in database
2025-06-11 18:22:04,856 - INFO - DIRECT FILTER: Found value for aspirin_325_mg: Yes, code: None
2025-06-11 18:22:04,856 - INFO - DIRECT FILTER: Creating status element for aspirin_325_mg
2025-06-11 18:22:04,858 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,859 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,859 - INFO - DIRECT FILTER: Added status element for aspirin_325_mg with value Yes
2025-06-11 18:22:04,860 - INFO - DIRECT FILTER: Processing ADJMEDS section #9
2025-06-11 18:22:04,860 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole)
2025-06-11 18:22:04,861 - INFO - DIRECT FILTER: Found medication aspirin_dipyridamole in database
2025-06-11 18:22:04,861 - INFO - DIRECT FILTER: Found value for aspirin_dipyridamole: Yes, code: None
2025-06-11 18:22:04,861 - INFO - DIRECT FILTER: Creating status element for aspirin_dipyridamole
2025-06-11 18:22:04,863 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,864 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,864 - INFO - DIRECT FILTER: Added status element for aspirin_dipyridamole with value Yes
2025-06-11 18:22:04,864 - INFO - DIRECT FILTER: Processing ADJMEDS section #10
2025-06-11 18:22:04,865 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar)
2025-06-11 18:22:04,865 - INFO - DIRECT FILTER: Found medication vorapaxar in database
2025-06-11 18:22:04,865 - INFO - DIRECT FILTER: Found value for vorapaxar: Yes, code: None
2025-06-11 18:22:04,865 - INFO - DIRECT FILTER: Creating status element for vorapaxar
2025-06-11 18:22:04,867 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,868 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,868 - INFO - DIRECT FILTER: Added status element for vorapaxar with value Yes
2025-06-11 18:22:04,868 - INFO - DIRECT FILTER: Processing ADJMEDS section #11
2025-06-11 18:22:04,869 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban)
2025-06-11 18:22:04,869 - INFO - DIRECT FILTER: Found medication apixaban in database
2025-06-11 18:22:04,869 - INFO - DIRECT FILTER: Found value for apixaban: Yes, code: None
2025-06-11 18:22:04,869 - INFO - DIRECT FILTER: Creating status element for apixaban
2025-06-11 18:22:04,871 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,883 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,885 - INFO - DIRECT FILTER: Added status element for apixaban with value Yes
2025-06-11 18:22:04,886 - INFO - DIRECT FILTER: Processing ADJMEDS section #12
2025-06-11 18:22:04,886 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran)
2025-06-11 18:22:04,886 - INFO - DIRECT FILTER: Found medication dabigatran in database
2025-06-11 18:22:04,887 - INFO - DIRECT FILTER: Found value for dabigatran: Yes, code: None
2025-06-11 18:22:04,887 - INFO - DIRECT FILTER: Creating status element for dabigatran
2025-06-11 18:22:04,892 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,895 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,896 - INFO - DIRECT FILTER: Added status element for dabigatran with value Yes
2025-06-11 18:22:04,900 - INFO - DIRECT FILTER: Processing ADJMEDS section #13
2025-06-11 18:22:04,901 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban)
2025-06-11 18:22:04,902 - INFO - DIRECT FILTER: Found medication edoxaban in database
2025-06-11 18:22:04,902 - INFO - DIRECT FILTER: Found value for edoxaban: Yes, code: None
2025-06-11 18:22:04,902 - INFO - DIRECT FILTER: Creating status element for edoxaban
2025-06-11 18:22:04,904 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,908 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,908 - INFO - DIRECT FILTER: Added status element for edoxaban with value Yes
2025-06-11 18:22:04,908 - INFO - DIRECT FILTER: Processing ADJMEDS section #14
2025-06-11 18:22:04,909 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban)
2025-06-11 18:22:04,909 - INFO - DIRECT FILTER: Found medication rivaroxaban in database
2025-06-11 18:22:04,909 - INFO - DIRECT FILTER: Found value for rivaroxaban: Yes, code: None
2025-06-11 18:22:04,910 - INFO - DIRECT FILTER: Creating status element for rivaroxaban
2025-06-11 18:22:04,912 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,913 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,913 - INFO - DIRECT FILTER: Added status element for rivaroxaban with value Yes
2025-06-11 18:22:04,916 - INFO - DIRECT FILTER: Processing ADJMEDS section #15
2025-06-11 18:22:04,916 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor)
2025-06-11 18:22:04,917 - INFO - DIRECT FILTER: Found medication cangrelor in database
2025-06-11 18:22:04,917 - INFO - DIRECT FILTER: Found value for cangrelor: Yes, code: None
2025-06-11 18:22:04,918 - INFO - DIRECT FILTER: Creating status element for cangrelor
2025-06-11 18:22:04,919 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,921 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,921 - INFO - DIRECT FILTER: Added status element for cangrelor with value Yes
2025-06-11 18:22:04,921 - INFO - DIRECT FILTER: Processing ADJMEDS section #16
2025-06-11 18:22:04,922 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel)
2025-06-11 18:22:04,922 - INFO - DIRECT FILTER: Found medication clopidogrel in database
2025-06-11 18:22:04,922 - INFO - DIRECT FILTER: Found value for clopidogrel: Yes, code: None
2025-06-11 18:22:04,923 - INFO - DIRECT FILTER: Creating status element for clopidogrel
2025-06-11 18:22:04,925 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,928 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,928 - INFO - DIRECT FILTER: Added status element for clopidogrel with value Yes
2025-06-11 18:22:04,929 - INFO - DIRECT FILTER: Processing ADJMEDS section #17
2025-06-11 18:22:04,929 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12)
2025-06-11 18:22:04,929 - INFO - DIRECT FILTER: Found medication other_p2y12 in database
2025-06-11 18:22:04,931 - INFO - DIRECT FILTER: Found value for other_p2y12: Yes, code: None
2025-06-11 18:22:04,933 - INFO - DIRECT FILTER: Creating status element for other_p2y12
2025-06-11 18:22:04,935 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,936 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,938 - INFO - DIRECT FILTER: Added status element for other_p2y12 with value Yes
2025-06-11 18:22:04,941 - INFO - DIRECT FILTER: Processing ADJMEDS section #18
2025-06-11 18:22:04,942 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel)
2025-06-11 18:22:04,942 - INFO - DIRECT FILTER: Found medication prasugrel in database
2025-06-11 18:22:04,942 - INFO - DIRECT FILTER: Found value for prasugrel: Yes, code: None
2025-06-11 18:22:04,943 - INFO - DIRECT FILTER: Creating status element for prasugrel
2025-06-11 18:22:04,945 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,947 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,948 - INFO - DIRECT FILTER: Added status element for prasugrel with value Yes
2025-06-11 18:22:04,949 - INFO - DIRECT FILTER: Processing ADJMEDS section #19
2025-06-11 18:22:04,950 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor)
2025-06-11 18:22:04,950 - INFO - DIRECT FILTER: Found medication ticagrelor in database
2025-06-11 18:22:04,950 - INFO - DIRECT FILTER: Found value for ticagrelor: Yes, code: None
2025-06-11 18:22:04,950 - INFO - DIRECT FILTER: Creating status element for ticagrelor
2025-06-11 18:22:04,952 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,953 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,954 - INFO - DIRECT FILTER: Added status element for ticagrelor with value Yes
2025-06-11 18:22:04,954 - INFO - DIRECT FILTER: Processing ADJMEDS section #20
2025-06-11 18:22:04,954 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine)
2025-06-11 18:22:04,955 - INFO - DIRECT FILTER: Found medication ticlopidine in database
2025-06-11 18:22:04,955 - INFO - DIRECT FILTER: Found value for ticlopidine: Yes, code: None
2025-06-11 18:22:04,955 - INFO - DIRECT FILTER: Creating status element for ticlopidine
2025-06-11 18:22:04,958 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:04,959 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:04,960 - INFO - DIRECT FILTER: Added status element for ticlopidine with value Yes
2025-06-11 18:22:04,960 - INFO - DIRECT FILTER: Processing section FADJMEDS
2025-06-11 18:22:04,960 - INFO - DIRECT FILTER: Found 0 sections with code FADJMEDS
2025-06-11 18:22:04,961 - INFO - DIRECT FILTER: No values found for section FADJMEDS, removing status elements
2025-06-11 18:22:46,734 - INFO - TABLE FIELDS: Called with field_id=6985, key_element=medications, section={'code': 'PREPROCMED'}
2025-06-11 18:22:46,735 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:48,343 - INFO - TABLE FIELDS: Called with field_id=12153, key_element=elements, section={'code': 'IPPEVENTS'}
2025-06-11 18:22:48,343 - INFO - TABLE FIELDS: No data found for key_element=elements
2025-06-11 18:22:49,026 - INFO - TABLE FIELDS: Called with field_id=10200, key_element=medications, section={'code': 'DCMEDS'}
2025-06-11 18:22:49,027 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:49,355 - INFO - TABLE FIELDS: Called with field_id=14940, key_element=medications, section={'code': 'ADJMEDS'}
2025-06-11 18:22:49,355 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:50,939 - INFO - TABLE FIELDS: Called with field_id=11990, key_element=elements, section={'code': 'FUPMEDS'}
2025-06-11 18:22:50,939 - INFO - TABLE FIELDS: No data found for key_element=elements
2025-06-11 18:22:51,521 - INFO - TABLE FIELDS: Called with field_id=14948, key_element=event_occurred, section={'code': 'FUPEVENTS'}
2025-06-11 18:22:51,521 - INFO - TABLE FIELDS: No data found for key_element=event_occurred
2025-06-11 18:22:52,182 - INFO - TABLE FIELDS: Called with field_id=15006, key_element=medications, section={'code': 'FADJMEDS'}
2025-06-11 18:22:52,182 - INFO - TABLE FIELDS: No data found for key_element=medications
2025-06-11 18:22:52,966 - INFO - Wrote field_result to debug file
2025-06-11 18:22:52,967 - INFO - DIRECT FILTER: Starting direct filtering of medication status elements
2025-06-11 18:22:52,987 - INFO - MEDICATION VALUES FROM DATABASE:
2025-06-11 18:22:52,987 - INFO - MEDICATION NAME MAPPINGS:
2025-06-11 18:22:52,988 - INFO -   heparin derivative -> heparin_derivative
2025-06-11 18:22:52,988 - INFO -   low molecular weight heparin -> low_molecular_weight_heparin
2025-06-11 18:22:52,989 - INFO -   unfractionated heparin -> unfractionated_heparin
2025-06-11 18:22:52,989 - INFO -   aspirin 81 to 100 mg -> aspirin_81_100_mg
2025-06-11 18:22:52,989 - INFO -   aspirin 101 to 324 mg -> aspirin_101_324_mg
2025-06-11 18:22:52,989 - INFO -   aspirin 325 mg -> aspirin_325_mg
2025-06-11 18:22:52,990 - INFO -   aspirin/dipyridamole -> aspirin_dipyridamole
2025-06-11 18:22:53,052 - INFO - Found pre_procedure_medications
2025-06-11 18:22:53,053 - INFO - Found 20 medications in pre_procedure_medications
2025-06-11 18:22:53,053 - INFO - PREPROCMED: fondaparinux
2025-06-11 18:22:53,054 - INFO -   Value: Past
2025-06-11 18:22:53,054 - INFO -   Field ID: 14883
2025-06-11 18:22:53,055 - INFO - PREPROCMED: heparin_derivative
2025-06-11 18:22:53,055 - INFO -   Value: Held
2025-06-11 18:22:53,056 - INFO -   Field ID: 14883
2025-06-11 18:22:53,056 - INFO - PREPROCMED: low_molecular_weight_heparin
2025-06-11 18:22:53,057 - INFO -   Value: Held
2025-06-11 18:22:53,057 - INFO -   Field ID: 14883
2025-06-11 18:22:53,057 - INFO - PREPROCMED: unfractionated_heparin
2025-06-11 18:22:53,058 - INFO -   Value: Held
2025-06-11 18:22:53,058 - INFO -   Field ID: 14883
2025-06-11 18:22:53,058 - INFO - PREPROCMED: warfarin
2025-06-11 18:22:53,059 - INFO -   Value: Held
2025-06-11 18:22:53,059 - INFO -   Field ID: 14883
2025-06-11 18:22:53,059 - INFO - PREPROCMED: aspirin_81_100_mg
2025-06-11 18:22:53,060 - INFO -   Value: Held
2025-06-11 18:22:53,060 - INFO -   Field ID: 14883
2025-06-11 18:22:53,060 - INFO - PREPROCMED: aspirin_101_324_mg
2025-06-11 18:22:53,061 - INFO -   Value: Held
2025-06-11 18:22:53,061 - INFO -   Field ID: 14883
2025-06-11 18:22:53,061 - INFO - PREPROCMED: aspirin_325_mg
2025-06-11 18:22:53,062 - INFO -   Value: Current
2025-06-11 18:22:53,062 - INFO -   Field ID: 14883
2025-06-11 18:22:53,062 - INFO - PREPROCMED: aspirin_dipyridamole
2025-06-11 18:22:53,063 - INFO -   Value: Held
2025-06-11 18:22:53,063 - INFO -   Field ID: 14883
2025-06-11 18:22:53,063 - INFO - PREPROCMED: vorapaxar
2025-06-11 18:22:53,064 - INFO -   Value: Past
2025-06-11 18:22:53,065 - INFO -   Field ID: 14883
2025-06-11 18:22:53,065 - INFO - PREPROCMED: apixaban
2025-06-11 18:22:53,066 - INFO -   Value: Held
2025-06-11 18:22:53,066 - INFO -   Field ID: 14883
2025-06-11 18:22:53,067 - INFO - PREPROCMED: dabigatran
2025-06-11 18:22:53,067 - INFO -   Value: Current
2025-06-11 18:22:53,067 - INFO -   Field ID: 14883
2025-06-11 18:22:53,068 - INFO - PREPROCMED: edoxaban
2025-06-11 18:22:53,068 - INFO -   Value: Held
2025-06-11 18:22:53,068 - INFO -   Field ID: 14883
2025-06-11 18:22:53,069 - INFO - PREPROCMED: rivaroxaban
2025-06-11 18:22:53,069 - INFO -   Value: Held
2025-06-11 18:22:53,069 - INFO -   Field ID: 14883
2025-06-11 18:22:53,070 - INFO - PREPROCMED: cangrelor
2025-06-11 18:22:53,070 - INFO -   Value: Past
2025-06-11 18:22:53,071 - INFO -   Field ID: 14883
2025-06-11 18:22:53,071 - INFO - PREPROCMED: clopidogrel
2025-06-11 18:22:53,071 - INFO -   Value: Current
2025-06-11 18:22:53,072 - INFO -   Field ID: 14883
2025-06-11 18:22:53,072 - INFO - PREPROCMED: other_p2y12
2025-06-11 18:22:53,072 - INFO -   Value: Held
2025-06-11 18:22:53,073 - INFO -   Field ID: 14883
2025-06-11 18:22:53,073 - INFO - PREPROCMED: prasugrel
2025-06-11 18:22:53,074 - INFO -   Value: Current
2025-06-11 18:22:53,074 - INFO -   Field ID: 14883
2025-06-11 18:22:53,074 - INFO - PREPROCMED: ticagrelor
2025-06-11 18:22:53,075 - INFO -   Value: Current
2025-06-11 18:22:53,075 - INFO -   Field ID: 14883
2025-06-11 18:22:53,075 - INFO - PREPROCMED: ticlopidine
2025-06-11 18:22:53,076 - INFO -   Value: Current
2025-06-11 18:22:53,076 - INFO -   Field ID: 14883
2025-06-11 18:22:53,076 - INFO - Found discharge_medications
2025-06-11 18:22:53,077 - INFO - Found 18 medications in discharge_medications
2025-06-11 18:22:53,077 - INFO - DCMEDS: fondaparinux
2025-06-11 18:22:53,078 - INFO -   Value: No - No Reason
2025-06-11 18:22:53,078 - INFO -   Field ID: 10205
2025-06-11 18:22:53,078 - INFO - DCMEDS: heparin_derivative
2025-06-11 18:22:53,079 - INFO -   Value: 
2025-06-11 18:22:53,079 - INFO -   Field ID: 10205
2025-06-11 18:22:53,080 - INFO - DCMEDS: low_molecular_weight_heparin
2025-06-11 18:22:53,081 - INFO -   Value: 
2025-06-11 18:22:53,081 - INFO -   Field ID: 10205
2025-06-11 18:22:53,082 - INFO - DCMEDS: unfractionated_heparin
2025-06-11 18:22:53,082 - INFO -   Value: 
2025-06-11 18:22:53,083 - INFO -   Field ID: 10205
2025-06-11 18:22:53,083 - INFO - DCMEDS: warfarin
2025-06-11 18:22:53,083 - INFO -   Value: 
2025-06-11 18:22:53,084 - INFO -   Field ID: 10205
2025-06-11 18:22:53,084 - INFO - DCMEDS: aspirin
2025-06-11 18:22:53,084 - INFO -   Value: Yes
2025-06-11 18:22:53,085 - INFO -   Field ID: 10205
2025-06-11 18:22:53,085 - INFO -   Found if_yes for aspirin
2025-06-11 18:22:53,085 - INFO -   Aspirin dose: 81 - 100 MG
2025-06-11 18:22:53,086 - INFO -   Dose field ID: 10207
2025-06-11 18:22:53,086 - INFO -   Added aspirin_81_100_mg with value Yes
2025-06-11 18:22:53,087 - INFO - DCMEDS: aspirin_dipyridamole
2025-06-11 18:22:53,088 - INFO -   Value: 
2025-06-11 18:22:53,088 - INFO -   Field ID: 10205
2025-06-11 18:22:53,088 - INFO - DCMEDS: vorapaxar
2025-06-11 18:22:53,089 - INFO -   Value: 
2025-06-11 18:22:53,089 - INFO -   Field ID: 10205
2025-06-11 18:22:53,090 - INFO - DCMEDS: apixaban
2025-06-11 18:22:53,090 - INFO -   Value: 
2025-06-11 18:22:53,090 - INFO -   Field ID: 10205
2025-06-11 18:22:53,091 - INFO - DCMEDS: dabigatran
2025-06-11 18:22:53,091 - INFO -   Value: 
2025-06-11 18:22:53,091 - INFO -   Field ID: 10205
2025-06-11 18:22:53,092 - INFO - DCMEDS: edoxaban
2025-06-11 18:22:53,092 - INFO -   Value: 
2025-06-11 18:22:53,092 - INFO -   Field ID: 10205
2025-06-11 18:22:53,093 - INFO - DCMEDS: rivaroxaban
2025-06-11 18:22:53,093 - INFO -   Value: 
2025-06-11 18:22:53,093 - INFO -   Field ID: 10205
2025-06-11 18:22:53,094 - INFO - DCMEDS: cangrelor
2025-06-11 18:22:53,094 - INFO -   Value: 
2025-06-11 18:22:53,095 - INFO -   Field ID: 10205
2025-06-11 18:22:53,096 - INFO - DCMEDS: clopidogrel
2025-06-11 18:22:53,097 - INFO -   Value: 
2025-06-11 18:22:53,098 - INFO -   Field ID: 10205
2025-06-11 18:22:53,098 - INFO - DCMEDS: other_p2y12
2025-06-11 18:22:53,098 - INFO -   Value: 
2025-06-11 18:22:53,099 - INFO -   Field ID: 10205
2025-06-11 18:22:53,099 - INFO - DCMEDS: prasugrel
2025-06-11 18:22:53,099 - INFO -   Value: 
2025-06-11 18:22:53,100 - INFO -   Field ID: 10205
2025-06-11 18:22:53,100 - INFO - DCMEDS: ticagrelor
2025-06-11 18:22:53,100 - INFO -   Value: 
2025-06-11 18:22:53,101 - INFO -   Field ID: 10205
2025-06-11 18:22:53,101 - INFO - DCMEDS: ticlopidine
2025-06-11 18:22:53,101 - INFO -   Value: 
2025-06-11 18:22:53,102 - INFO -   Field ID: 10205
2025-06-11 18:22:53,102 - INFO - Found in_hospital_adjudication
2025-06-11 18:22:53,102 - INFO - Found 20 medications in in_hospital_adjudication
2025-06-11 18:22:53,103 - INFO - ADJMEDS: fondaparinux
2025-06-11 18:22:53,104 - INFO -   Value: Yes
2025-06-11 18:22:53,104 - INFO -   Field ID: 14941
2025-06-11 18:22:53,105 - INFO - ADJMEDS: heparin_derivative
2025-06-11 18:22:53,105 - INFO -   Value: Yes
2025-06-11 18:22:53,106 - INFO -   Field ID: 14941
2025-06-11 18:22:53,106 - INFO - ADJMEDS: low_molecular_weight_heparin
2025-06-11 18:22:53,106 - INFO -   Value: Yes
2025-06-11 18:22:53,107 - INFO -   Field ID: 14941
2025-06-11 18:22:53,107 - INFO - ADJMEDS: unfractionated_heparin
2025-06-11 18:22:53,107 - INFO -   Value: Yes
2025-06-11 18:22:53,108 - INFO -   Field ID: 14941
2025-06-11 18:22:53,108 - INFO - ADJMEDS: warfarin
2025-06-11 18:22:53,108 - INFO -   Value: No
2025-06-11 18:22:53,109 - INFO -   Field ID: 14941
2025-06-11 18:22:53,109 - INFO - ADJMEDS: aspirin_81_100_mg
2025-06-11 18:22:53,110 - INFO -   Value: Yes
2025-06-11 18:22:53,110 - INFO -   Field ID: 14941
2025-06-11 18:22:53,110 - INFO - ADJMEDS: aspirin_101_324_mg
2025-06-11 18:22:53,111 - INFO -   Value: Yes
2025-06-11 18:22:53,111 - INFO -   Field ID: 14941
2025-06-11 18:22:53,112 - INFO - ADJMEDS: aspirin_325_mg
2025-06-11 18:22:53,112 - INFO -   Value: Yes
2025-06-11 18:22:53,113 - INFO -   Field ID: 14941
2025-06-11 18:22:53,113 - INFO - ADJMEDS: aspirin_dipyridamole
2025-06-11 18:22:53,113 - INFO -   Value: Yes
2025-06-11 18:22:53,114 - INFO -   Field ID: 14941
2025-06-11 18:22:53,114 - INFO - ADJMEDS: vorapaxar
2025-06-11 18:22:53,115 - INFO -   Value: Yes
2025-06-11 18:22:53,115 - INFO -   Field ID: 14941
2025-06-11 18:22:53,115 - INFO - ADJMEDS: apixaban
2025-06-11 18:22:53,116 - INFO -   Value: Yes
2025-06-11 18:22:53,116 - INFO -   Field ID: 14941
2025-06-11 18:22:53,117 - INFO - ADJMEDS: dabigatran
2025-06-11 18:22:53,117 - INFO -   Value: Yes
2025-06-11 18:22:53,117 - INFO -   Field ID: 14941
2025-06-11 18:22:53,118 - INFO - ADJMEDS: edoxaban
2025-06-11 18:22:53,118 - INFO -   Value: Yes
2025-06-11 18:22:53,119 - INFO -   Field ID: 14941
2025-06-11 18:22:53,119 - INFO - ADJMEDS: rivaroxaban
2025-06-11 18:22:53,120 - INFO -   Value: Yes
2025-06-11 18:22:53,120 - INFO -   Field ID: 14941
2025-06-11 18:22:53,121 - INFO - ADJMEDS: cangrelor
2025-06-11 18:22:53,121 - INFO -   Value: Yes
2025-06-11 18:22:53,122 - INFO -   Field ID: 14941
2025-06-11 18:22:53,122 - INFO - ADJMEDS: clopidogrel
2025-06-11 18:22:53,122 - INFO -   Value: Yes
2025-06-11 18:22:53,123 - INFO -   Field ID: 14941
2025-06-11 18:22:53,123 - INFO - ADJMEDS: other_p2y12
2025-06-11 18:22:53,123 - INFO -   Value: Yes
2025-06-11 18:22:53,124 - INFO -   Field ID: 14941
2025-06-11 18:22:53,125 - INFO - ADJMEDS: prasugrel
2025-06-11 18:22:53,125 - INFO -   Value: Yes
2025-06-11 18:22:53,126 - INFO -   Field ID: 14941
2025-06-11 18:22:53,126 - INFO - ADJMEDS: ticagrelor
2025-06-11 18:22:53,127 - INFO -   Value: Yes
2025-06-11 18:22:53,127 - INFO -   Field ID: 14941
2025-06-11 18:22:53,127 - INFO - ADJMEDS: ticlopidine
2025-06-11 18:22:53,128 - INFO -   Value: Yes
2025-06-11 18:22:53,129 - INFO -   Field ID: 14941
2025-06-11 18:22:53,129 - INFO - DIRECT FILTER: Processing section PREPROCMED
2025-06-11 18:22:53,130 - INFO - DIRECT FILTER: Found 20 sections with code PREPROCMED
2025-06-11 18:22:53,130 - INFO - DIRECT FILTER: Processing section PREPROCMED #1
2025-06-11 18:22:53,130 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #1
2025-06-11 18:22:53,131 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux) in section PREPROCMED #1
2025-06-11 18:22:53,131 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:22:53,132 - INFO - DIRECT FILTER: Found value for fondaparinux: Past, code: None
2025-06-11 18:22:53,132 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:22:53,135 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:22:53,137 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:22:53,138 - INFO - DIRECT FILTER: Added status element for fondaparinux with value Past in section PREPROCMED #1
2025-06-11 18:22:53,138 - INFO - DIRECT FILTER: Processing section PREPROCMED #2
2025-06-11 18:22:53,139 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #2
2025-06-11 18:22:53,139 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative) in section PREPROCMED #2
2025-06-11 18:22:53,139 - INFO - DIRECT FILTER: Found medication heparin_derivative in database
2025-06-11 18:22:53,140 - INFO - DIRECT FILTER: Found value for heparin_derivative: Held, code: None
2025-06-11 18:22:53,140 - INFO - DIRECT FILTER: Creating status element for heparin_derivative
2025-06-11 18:22:53,143 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,144 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,145 - INFO - DIRECT FILTER: Added status element for heparin_derivative with value Held in section PREPROCMED #2
2025-06-11 18:22:53,145 - INFO - DIRECT FILTER: Processing section PREPROCMED #3
2025-06-11 18:22:53,146 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #3
2025-06-11 18:22:53,147 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin) in section PREPROCMED #3
2025-06-11 18:22:53,147 - INFO - DIRECT FILTER: Found medication low_molecular_weight_heparin in database
2025-06-11 18:22:53,148 - INFO - DIRECT FILTER: Found value for low_molecular_weight_heparin: Held, code: None
2025-06-11 18:22:53,148 - INFO - DIRECT FILTER: Creating status element for low_molecular_weight_heparin
2025-06-11 18:22:53,150 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,152 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,152 - INFO - DIRECT FILTER: Added status element for low_molecular_weight_heparin with value Held in section PREPROCMED #3
2025-06-11 18:22:53,152 - INFO - DIRECT FILTER: Processing section PREPROCMED #4
2025-06-11 18:22:53,153 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #4
2025-06-11 18:22:53,153 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin) in section PREPROCMED #4
2025-06-11 18:22:53,154 - INFO - DIRECT FILTER: Found medication unfractionated_heparin in database
2025-06-11 18:22:53,154 - INFO - DIRECT FILTER: Found value for unfractionated_heparin: Held, code: None
2025-06-11 18:22:53,155 - INFO - DIRECT FILTER: Creating status element for unfractionated_heparin
2025-06-11 18:22:53,156 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,158 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,158 - INFO - DIRECT FILTER: Added status element for unfractionated_heparin with value Held in section PREPROCMED #4
2025-06-11 18:22:53,159 - INFO - DIRECT FILTER: Processing section PREPROCMED #5
2025-06-11 18:22:53,159 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #5
2025-06-11 18:22:53,159 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin) in section PREPROCMED #5
2025-06-11 18:22:53,160 - INFO - DIRECT FILTER: Found medication warfarin in database
2025-06-11 18:22:53,160 - INFO - DIRECT FILTER: Found value for warfarin: Held, code: None
2025-06-11 18:22:53,161 - INFO - DIRECT FILTER: Creating status element for warfarin
2025-06-11 18:22:53,164 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,166 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,166 - INFO - DIRECT FILTER: Added status element for warfarin with value Held in section PREPROCMED #5
2025-06-11 18:22:53,167 - INFO - DIRECT FILTER: Processing section PREPROCMED #6
2025-06-11 18:22:53,167 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #6
2025-06-11 18:22:53,168 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 81 to 100 mg (normalized: aspirin_81_100_mg) in section PREPROCMED #6
2025-06-11 18:22:53,168 - INFO - DIRECT FILTER: Found medication aspirin_81_100_mg in database
2025-06-11 18:22:53,168 - INFO - DIRECT FILTER: Found value for aspirin_81_100_mg: Held, code: None
2025-06-11 18:22:53,169 - INFO - DIRECT FILTER: Creating status element for aspirin_81_100_mg
2025-06-11 18:22:53,172 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,173 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,174 - INFO - DIRECT FILTER: Added status element for aspirin_81_100_mg with value Held in section PREPROCMED #6
2025-06-11 18:22:53,174 - INFO - DIRECT FILTER: Processing section PREPROCMED #7
2025-06-11 18:22:53,175 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #7
2025-06-11 18:22:53,175 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 101 to 324 mg (normalized: aspirin_101_324_mg) in section PREPROCMED #7
2025-06-11 18:22:53,176 - INFO - DIRECT FILTER: Found medication aspirin_101_324_mg in database
2025-06-11 18:22:53,176 - INFO - DIRECT FILTER: Found value for aspirin_101_324_mg: Held, code: None
2025-06-11 18:22:53,176 - INFO - DIRECT FILTER: Creating status element for aspirin_101_324_mg
2025-06-11 18:22:53,179 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,182 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,182 - INFO - DIRECT FILTER: Added status element for aspirin_101_324_mg with value Held in section PREPROCMED #7
2025-06-11 18:22:53,183 - INFO - DIRECT FILTER: Processing section PREPROCMED #8
2025-06-11 18:22:53,183 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #8
2025-06-11 18:22:53,183 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 325 mg (normalized: aspirin_325_mg) in section PREPROCMED #8
2025-06-11 18:22:53,184 - INFO - DIRECT FILTER: Found medication aspirin_325_mg in database
2025-06-11 18:22:53,184 - INFO - DIRECT FILTER: Found value for aspirin_325_mg: Current, code: None
2025-06-11 18:22:53,184 - INFO - DIRECT FILTER: Creating status element for aspirin_325_mg
2025-06-11 18:22:53,188 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:53,190 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:53,191 - INFO - DIRECT FILTER: Added status element for aspirin_325_mg with value Current in section PREPROCMED #8
2025-06-11 18:22:53,191 - INFO - DIRECT FILTER: Processing section PREPROCMED #9
2025-06-11 18:22:53,192 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #9
2025-06-11 18:22:53,192 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole) in section PREPROCMED #9
2025-06-11 18:22:53,192 - INFO - DIRECT FILTER: Found medication aspirin_dipyridamole in database
2025-06-11 18:22:53,193 - INFO - DIRECT FILTER: Found value for aspirin_dipyridamole: Held, code: None
2025-06-11 18:22:53,193 - INFO - DIRECT FILTER: Creating status element for aspirin_dipyridamole
2025-06-11 18:22:53,197 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,199 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,199 - INFO - DIRECT FILTER: Added status element for aspirin_dipyridamole with value Held in section PREPROCMED #9
2025-06-11 18:22:53,199 - INFO - DIRECT FILTER: Processing section PREPROCMED #10
2025-06-11 18:22:53,200 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #10
2025-06-11 18:22:53,200 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar) in section PREPROCMED #10
2025-06-11 18:22:53,201 - INFO - DIRECT FILTER: Found medication vorapaxar in database
2025-06-11 18:22:53,201 - INFO - DIRECT FILTER: Found value for vorapaxar: Past, code: None
2025-06-11 18:22:53,201 - INFO - DIRECT FILTER: Creating status element for vorapaxar
2025-06-11 18:22:53,205 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:22:53,207 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:22:53,208 - INFO - DIRECT FILTER: Added status element for vorapaxar with value Past in section PREPROCMED #10
2025-06-11 18:22:53,208 - INFO - DIRECT FILTER: Processing section PREPROCMED #11
2025-06-11 18:22:53,208 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #11
2025-06-11 18:22:53,209 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban) in section PREPROCMED #11
2025-06-11 18:22:53,209 - INFO - DIRECT FILTER: Found medication apixaban in database
2025-06-11 18:22:53,209 - INFO - DIRECT FILTER: Found value for apixaban: Held, code: None
2025-06-11 18:22:53,210 - INFO - DIRECT FILTER: Creating status element for apixaban
2025-06-11 18:22:53,213 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,215 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,216 - INFO - DIRECT FILTER: Added status element for apixaban with value Held in section PREPROCMED #11
2025-06-11 18:22:53,216 - INFO - DIRECT FILTER: Processing section PREPROCMED #12
2025-06-11 18:22:53,217 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #12
2025-06-11 18:22:53,217 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran) in section PREPROCMED #12
2025-06-11 18:22:53,217 - INFO - DIRECT FILTER: Found medication dabigatran in database
2025-06-11 18:22:53,218 - INFO - DIRECT FILTER: Found value for dabigatran: Current, code: None
2025-06-11 18:22:53,218 - INFO - DIRECT FILTER: Creating status element for dabigatran
2025-06-11 18:22:53,222 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:53,224 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:53,225 - INFO - DIRECT FILTER: Added status element for dabigatran with value Current in section PREPROCMED #12
2025-06-11 18:22:53,225 - INFO - DIRECT FILTER: Processing section PREPROCMED #13
2025-06-11 18:22:53,225 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #13
2025-06-11 18:22:53,226 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban) in section PREPROCMED #13
2025-06-11 18:22:53,226 - INFO - DIRECT FILTER: Found medication edoxaban in database
2025-06-11 18:22:53,226 - INFO - DIRECT FILTER: Found value for edoxaban: Held, code: None
2025-06-11 18:22:53,227 - INFO - DIRECT FILTER: Creating status element for edoxaban
2025-06-11 18:22:53,229 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,231 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,232 - INFO - DIRECT FILTER: Added status element for edoxaban with value Held in section PREPROCMED #13
2025-06-11 18:22:53,232 - INFO - DIRECT FILTER: Processing section PREPROCMED #14
2025-06-11 18:22:53,233 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #14
2025-06-11 18:22:53,233 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban) in section PREPROCMED #14
2025-06-11 18:22:53,234 - INFO - DIRECT FILTER: Found medication rivaroxaban in database
2025-06-11 18:22:53,234 - INFO - DIRECT FILTER: Found value for rivaroxaban: Held, code: None
2025-06-11 18:22:53,234 - INFO - DIRECT FILTER: Creating status element for rivaroxaban
2025-06-11 18:22:53,237 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,240 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,240 - INFO - DIRECT FILTER: Added status element for rivaroxaban with value Held in section PREPROCMED #14
2025-06-11 18:22:53,241 - INFO - DIRECT FILTER: Processing section PREPROCMED #15
2025-06-11 18:22:53,241 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #15
2025-06-11 18:22:53,242 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor) in section PREPROCMED #15
2025-06-11 18:22:53,242 - INFO - DIRECT FILTER: Found medication cangrelor in database
2025-06-11 18:22:53,242 - INFO - DIRECT FILTER: Found value for cangrelor: Past, code: None
2025-06-11 18:22:53,243 - INFO - DIRECT FILTER: Creating status element for cangrelor
2025-06-11 18:22:53,245 - INFO - DIRECT FILTER: Looked up code for Past: 100001070
2025-06-11 18:22:53,247 - INFO - DIRECT FILTER: Added value: 100001070 - Past
2025-06-11 18:22:53,248 - INFO - DIRECT FILTER: Added status element for cangrelor with value Past in section PREPROCMED #15
2025-06-11 18:22:53,248 - INFO - DIRECT FILTER: Processing section PREPROCMED #16
2025-06-11 18:22:53,249 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #16
2025-06-11 18:22:53,249 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel) in section PREPROCMED #16
2025-06-11 18:22:53,249 - INFO - DIRECT FILTER: Found medication clopidogrel in database
2025-06-11 18:22:53,250 - INFO - DIRECT FILTER: Found value for clopidogrel: Current, code: None
2025-06-11 18:22:53,250 - INFO - DIRECT FILTER: Creating status element for clopidogrel
2025-06-11 18:22:53,253 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:53,256 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:53,256 - INFO - DIRECT FILTER: Added status element for clopidogrel with value Current in section PREPROCMED #16
2025-06-11 18:22:53,257 - INFO - DIRECT FILTER: Processing section PREPROCMED #17
2025-06-11 18:22:53,257 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #17
2025-06-11 18:22:53,257 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12) in section PREPROCMED #17
2025-06-11 18:22:53,258 - INFO - DIRECT FILTER: Found medication other_p2y12 in database
2025-06-11 18:22:53,258 - INFO - DIRECT FILTER: Found value for other_p2y12: Held, code: None
2025-06-11 18:22:53,258 - INFO - DIRECT FILTER: Creating status element for other_p2y12
2025-06-11 18:22:53,261 - INFO - DIRECT FILTER: Looked up code for Held: 100001010
2025-06-11 18:22:53,264 - INFO - DIRECT FILTER: Added value: 100001010 - Held
2025-06-11 18:22:53,264 - INFO - DIRECT FILTER: Added status element for other_p2y12 with value Held in section PREPROCMED #17
2025-06-11 18:22:53,265 - INFO - DIRECT FILTER: Processing section PREPROCMED #18
2025-06-11 18:22:53,265 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #18
2025-06-11 18:22:53,265 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel) in section PREPROCMED #18
2025-06-11 18:22:53,266 - INFO - DIRECT FILTER: Found medication prasugrel in database
2025-06-11 18:22:53,266 - INFO - DIRECT FILTER: Found value for prasugrel: Current, code: None
2025-06-11 18:22:53,266 - INFO - DIRECT FILTER: Creating status element for prasugrel
2025-06-11 18:22:53,268 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:53,270 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:53,271 - INFO - DIRECT FILTER: Added status element for prasugrel with value Current in section PREPROCMED #18
2025-06-11 18:22:53,272 - INFO - DIRECT FILTER: Processing section PREPROCMED #19
2025-06-11 18:22:53,272 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #19
2025-06-11 18:22:53,273 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor) in section PREPROCMED #19
2025-06-11 18:22:53,273 - INFO - DIRECT FILTER: Found medication ticagrelor in database
2025-06-11 18:22:53,273 - INFO - DIRECT FILTER: Found value for ticagrelor: Current, code: None
2025-06-11 18:22:53,273 - INFO - DIRECT FILTER: Creating status element for ticagrelor
2025-06-11 18:22:53,276 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:53,277 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:53,278 - INFO - DIRECT FILTER: Added status element for ticagrelor with value Current in section PREPROCMED #19
2025-06-11 18:22:53,278 - INFO - DIRECT FILTER: Processing section PREPROCMED #20
2025-06-11 18:22:53,278 - INFO - DIRECT FILTER: Found 1 medication elements in section PREPROCMED #20
2025-06-11 18:22:53,279 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine) in section PREPROCMED #20
2025-06-11 18:22:53,279 - INFO - DIRECT FILTER: Found medication ticlopidine in database
2025-06-11 18:22:53,280 - INFO - DIRECT FILTER: Found value for ticlopidine: Current, code: None
2025-06-11 18:22:53,280 - INFO - DIRECT FILTER: Creating status element for ticlopidine
2025-06-11 18:22:53,284 - INFO - DIRECT FILTER: Looked up code for Current: 100000987
2025-06-11 18:22:53,285 - INFO - DIRECT FILTER: Added value: 100000987 - Current
2025-06-11 18:22:53,285 - INFO - DIRECT FILTER: Added status element for ticlopidine with value Current in section PREPROCMED #20
2025-06-11 18:22:53,286 - INFO - DIRECT FILTER: Processing section DCMEDS
2025-06-11 18:22:53,287 - INFO - DIRECT FILTER: Found 18 sections with code DCMEDS
2025-06-11 18:22:53,287 - INFO - DIRECT FILTER: Special handling for DCMEDS section
2025-06-11 18:22:53,288 - INFO - DIRECT FILTER: Removing status element with displayName 'Discharge Medication Dose' from DCMEDS
2025-06-11 18:22:53,289 - INFO - DIRECT FILTER: Processing DCMEDS section #1
2025-06-11 18:22:53,289 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux)
2025-06-11 18:22:53,290 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:22:53,290 - INFO - DIRECT FILTER: Found value for fondaparinux: No - No Reason, code: None
2025-06-11 18:22:53,290 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:22:53,292 - INFO - DIRECT FILTER: Looked up code for No - No Reason: 100001048
2025-06-11 18:22:53,294 - INFO - DIRECT FILTER: Added value: 100001048 - No - No Reason
2025-06-11 18:22:53,294 - INFO - DIRECT FILTER: Added status element for fondaparinux with value No - No Reason
2025-06-11 18:22:53,294 - INFO - DIRECT FILTER: Processing DCMEDS section #2
2025-06-11 18:22:53,295 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative)
2025-06-11 18:22:53,296 - INFO - DIRECT FILTER: Medication heparin_derivative not found directly, trying alternatives
2025-06-11 18:22:53,296 - INFO - DIRECT FILTER: No medication info found for heparin_derivative, skipping
2025-06-11 18:22:53,297 - INFO - DIRECT FILTER: Processing DCMEDS section #3
2025-06-11 18:22:53,297 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin)
2025-06-11 18:22:53,298 - INFO - DIRECT FILTER: Medication low_molecular_weight_heparin not found directly, trying alternatives
2025-06-11 18:22:53,298 - INFO - DIRECT FILTER: No medication info found for low_molecular_weight_heparin, skipping
2025-06-11 18:22:53,298 - INFO - DIRECT FILTER: Processing DCMEDS section #4
2025-06-11 18:22:53,299 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin)
2025-06-11 18:22:53,299 - INFO - DIRECT FILTER: Medication unfractionated_heparin not found directly, trying alternatives
2025-06-11 18:22:53,299 - INFO - DIRECT FILTER: No medication info found for unfractionated_heparin, skipping
2025-06-11 18:22:53,300 - INFO - DIRECT FILTER: Processing DCMEDS section #5
2025-06-11 18:22:53,300 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin)
2025-06-11 18:22:53,301 - INFO - DIRECT FILTER: Medication warfarin not found directly, trying alternatives
2025-06-11 18:22:53,301 - INFO - DIRECT FILTER: No medication info found for warfarin, skipping
2025-06-11 18:22:53,301 - INFO - DIRECT FILTER: Processing DCMEDS section #6
2025-06-11 18:22:53,302 - INFO - DIRECT FILTER: Processing medication #1: Aspirin (normalized: aspirin)
2025-06-11 18:22:53,302 - INFO - DIRECT FILTER: Found medication aspirin in database
2025-06-11 18:22:53,302 - INFO - DIRECT FILTER: Found value for aspirin: Yes, code: None
2025-06-11 18:22:53,303 - INFO - DIRECT FILTER: Creating status element for aspirin
2025-06-11 18:22:53,306 - INFO - DIRECT FILTER: Looked up code for Yes: 100001247
2025-06-11 18:22:53,308 - INFO - DIRECT FILTER: Added value: 100001247 - Yes
2025-06-11 18:22:53,308 - INFO - DIRECT FILTER: Added status element for aspirin with value Yes
2025-06-11 18:22:53,308 - INFO - DIRECT FILTER: Processing DCMEDS section #7
2025-06-11 18:22:53,309 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole)
2025-06-11 18:22:53,309 - INFO - DIRECT FILTER: Medication aspirin_dipyridamole not found directly, trying alternatives
2025-06-11 18:22:53,309 - INFO - DIRECT FILTER: No medication info found for aspirin_dipyridamole, skipping
2025-06-11 18:22:53,310 - INFO - DIRECT FILTER: Processing DCMEDS section #8
2025-06-11 18:22:53,310 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar)
2025-06-11 18:22:53,311 - INFO - DIRECT FILTER: Medication vorapaxar not found directly, trying alternatives
2025-06-11 18:22:53,311 - INFO - DIRECT FILTER: No medication info found for vorapaxar, skipping
2025-06-11 18:22:53,312 - INFO - DIRECT FILTER: Processing DCMEDS section #9
2025-06-11 18:22:53,313 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban)
2025-06-11 18:22:53,314 - INFO - DIRECT FILTER: Medication apixaban not found directly, trying alternatives
2025-06-11 18:22:53,314 - INFO - DIRECT FILTER: No medication info found for apixaban, skipping
2025-06-11 18:22:53,315 - INFO - DIRECT FILTER: Processing DCMEDS section #10
2025-06-11 18:22:53,315 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran)
2025-06-11 18:22:53,316 - INFO - DIRECT FILTER: Medication dabigatran not found directly, trying alternatives
2025-06-11 18:22:53,316 - INFO - DIRECT FILTER: No medication info found for dabigatran, skipping
2025-06-11 18:22:53,316 - INFO - DIRECT FILTER: Processing DCMEDS section #11
2025-06-11 18:22:53,317 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban)
2025-06-11 18:22:53,317 - INFO - DIRECT FILTER: Medication edoxaban not found directly, trying alternatives
2025-06-11 18:22:53,317 - INFO - DIRECT FILTER: No medication info found for edoxaban, skipping
2025-06-11 18:22:53,318 - INFO - DIRECT FILTER: Processing DCMEDS section #12
2025-06-11 18:22:53,318 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban)
2025-06-11 18:22:53,318 - INFO - DIRECT FILTER: Medication rivaroxaban not found directly, trying alternatives
2025-06-11 18:22:53,319 - INFO - DIRECT FILTER: No medication info found for rivaroxaban, skipping
2025-06-11 18:22:53,319 - INFO - DIRECT FILTER: Processing DCMEDS section #13
2025-06-11 18:22:53,320 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor)
2025-06-11 18:22:53,320 - INFO - DIRECT FILTER: Medication cangrelor not found directly, trying alternatives
2025-06-11 18:22:53,321 - INFO - DIRECT FILTER: No medication info found for cangrelor, skipping
2025-06-11 18:22:53,321 - INFO - DIRECT FILTER: Processing DCMEDS section #14
2025-06-11 18:22:53,321 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel)
2025-06-11 18:22:53,322 - INFO - DIRECT FILTER: Medication clopidogrel not found directly, trying alternatives
2025-06-11 18:22:53,322 - INFO - DIRECT FILTER: No medication info found for clopidogrel, skipping
2025-06-11 18:22:53,323 - INFO - DIRECT FILTER: Processing DCMEDS section #15
2025-06-11 18:22:53,323 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12)
2025-06-11 18:22:53,324 - INFO - DIRECT FILTER: Medication other_p2y12 not found directly, trying alternatives
2025-06-11 18:22:53,324 - INFO - DIRECT FILTER: No medication info found for other_p2y12, skipping
2025-06-11 18:22:53,324 - INFO - DIRECT FILTER: Processing DCMEDS section #16
2025-06-11 18:22:53,325 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel)
2025-06-11 18:22:53,325 - INFO - DIRECT FILTER: Medication prasugrel not found directly, trying alternatives
2025-06-11 18:22:53,326 - INFO - DIRECT FILTER: No medication info found for prasugrel, skipping
2025-06-11 18:22:53,326 - INFO - DIRECT FILTER: Processing DCMEDS section #17
2025-06-11 18:22:53,326 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor)
2025-06-11 18:22:53,327 - INFO - DIRECT FILTER: Medication ticagrelor not found directly, trying alternatives
2025-06-11 18:22:53,327 - INFO - DIRECT FILTER: No medication info found for ticagrelor, skipping
2025-06-11 18:22:53,327 - INFO - DIRECT FILTER: Processing DCMEDS section #18
2025-06-11 18:22:53,329 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine)
2025-06-11 18:22:53,330 - INFO - DIRECT FILTER: Medication ticlopidine not found directly, trying alternatives
2025-06-11 18:22:53,330 - INFO - DIRECT FILTER: No medication info found for ticlopidine, skipping
2025-06-11 18:22:53,330 - INFO - DIRECT FILTER: Processing section ADJMEDS
2025-06-11 18:22:53,331 - INFO - DIRECT FILTER: Found 20 sections with code ADJMEDS
2025-06-11 18:22:53,332 - INFO - DIRECT FILTER: Special handling for ADJMEDS section
2025-06-11 18:22:53,332 - INFO - DIRECT FILTER: Processing ADJMEDS section #1
2025-06-11 18:22:53,333 - INFO - DIRECT FILTER: Processing medication #1: Fondaparinux (normalized: fondaparinux)
2025-06-11 18:22:53,333 - INFO - DIRECT FILTER: Found medication fondaparinux in database
2025-06-11 18:22:53,333 - INFO - DIRECT FILTER: Found value for fondaparinux: Yes, code: None
2025-06-11 18:22:53,334 - INFO - DIRECT FILTER: Creating status element for fondaparinux
2025-06-11 18:22:53,337 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,339 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,339 - INFO - DIRECT FILTER: Added status element for fondaparinux with value Yes
2025-06-11 18:22:53,340 - INFO - DIRECT FILTER: Processing ADJMEDS section #2
2025-06-11 18:22:53,340 - INFO - DIRECT FILTER: Processing medication #1: Heparin Derivative (normalized: heparin_derivative)
2025-06-11 18:22:53,341 - INFO - DIRECT FILTER: Found medication heparin_derivative in database
2025-06-11 18:22:53,341 - INFO - DIRECT FILTER: Found value for heparin_derivative: Yes, code: None
2025-06-11 18:22:53,341 - INFO - DIRECT FILTER: Creating status element for heparin_derivative
2025-06-11 18:22:53,343 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,347 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,348 - INFO - DIRECT FILTER: Added status element for heparin_derivative with value Yes
2025-06-11 18:22:53,348 - INFO - DIRECT FILTER: Processing ADJMEDS section #3
2025-06-11 18:22:53,348 - INFO - DIRECT FILTER: Processing medication #1: Low Molecular Weight Heparin (normalized: low_molecular_weight_heparin)
2025-06-11 18:22:53,349 - INFO - DIRECT FILTER: Found medication low_molecular_weight_heparin in database
2025-06-11 18:22:53,349 - INFO - DIRECT FILTER: Found value for low_molecular_weight_heparin: Yes, code: None
2025-06-11 18:22:53,349 - INFO - DIRECT FILTER: Creating status element for low_molecular_weight_heparin
2025-06-11 18:22:53,352 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,354 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,355 - INFO - DIRECT FILTER: Added status element for low_molecular_weight_heparin with value Yes
2025-06-11 18:22:53,355 - INFO - DIRECT FILTER: Processing ADJMEDS section #4
2025-06-11 18:22:53,356 - INFO - DIRECT FILTER: Processing medication #1: Unfractionated Heparin (normalized: unfractionated_heparin)
2025-06-11 18:22:53,356 - INFO - DIRECT FILTER: Found medication unfractionated_heparin in database
2025-06-11 18:22:53,357 - INFO - DIRECT FILTER: Found value for unfractionated_heparin: Yes, code: None
2025-06-11 18:22:53,357 - INFO - DIRECT FILTER: Creating status element for unfractionated_heparin
2025-06-11 18:22:53,359 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,360 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,361 - INFO - DIRECT FILTER: Added status element for unfractionated_heparin with value Yes
2025-06-11 18:22:53,361 - INFO - DIRECT FILTER: Processing ADJMEDS section #5
2025-06-11 18:22:53,362 - INFO - DIRECT FILTER: Processing medication #1: Warfarin (normalized: warfarin)
2025-06-11 18:22:53,362 - INFO - DIRECT FILTER: Found medication warfarin in database
2025-06-11 18:22:53,363 - INFO - DIRECT FILTER: Found value for warfarin: No, code: None
2025-06-11 18:22:53,363 - INFO - DIRECT FILTER: Creating status element for warfarin
2025-06-11 18:22:53,365 - INFO - DIRECT FILTER: Looked up code for No: 100014173
2025-06-11 18:22:53,367 - INFO - DIRECT FILTER: Added value: 100014173 - No
2025-06-11 18:22:53,368 - INFO - DIRECT FILTER: Added status element for warfarin with value No
2025-06-11 18:22:53,368 - INFO - DIRECT FILTER: Processing ADJMEDS section #6
2025-06-11 18:22:53,368 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 81 to 100 mg (normalized: aspirin_81_100_mg)
2025-06-11 18:22:53,369 - INFO - DIRECT FILTER: Found medication aspirin_81_100_mg in database
2025-06-11 18:22:53,369 - INFO - DIRECT FILTER: Found value for aspirin_81_100_mg: Yes, code: None
2025-06-11 18:22:53,369 - INFO - DIRECT FILTER: Creating status element for aspirin_81_100_mg
2025-06-11 18:22:53,373 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,374 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,375 - INFO - DIRECT FILTER: Added status element for aspirin_81_100_mg with value Yes
2025-06-11 18:22:53,375 - INFO - DIRECT FILTER: Processing ADJMEDS section #7
2025-06-11 18:22:53,376 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 101 to 324 mg (normalized: aspirin_101_324_mg)
2025-06-11 18:22:53,376 - INFO - DIRECT FILTER: Found medication aspirin_101_324_mg in database
2025-06-11 18:22:53,377 - INFO - DIRECT FILTER: Found value for aspirin_101_324_mg: Yes, code: None
2025-06-11 18:22:53,377 - INFO - DIRECT FILTER: Creating status element for aspirin_101_324_mg
2025-06-11 18:22:53,380 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,382 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,383 - INFO - DIRECT FILTER: Added status element for aspirin_101_324_mg with value Yes
2025-06-11 18:22:53,383 - INFO - DIRECT FILTER: Processing ADJMEDS section #8
2025-06-11 18:22:53,384 - INFO - DIRECT FILTER: Processing medication #1: Aspirin 325 mg (normalized: aspirin_325_mg)
2025-06-11 18:22:53,384 - INFO - DIRECT FILTER: Found medication aspirin_325_mg in database
2025-06-11 18:22:53,384 - INFO - DIRECT FILTER: Found value for aspirin_325_mg: Yes, code: None
2025-06-11 18:22:53,385 - INFO - DIRECT FILTER: Creating status element for aspirin_325_mg
2025-06-11 18:22:53,389 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,391 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,391 - INFO - DIRECT FILTER: Added status element for aspirin_325_mg with value Yes
2025-06-11 18:22:53,392 - INFO - DIRECT FILTER: Processing ADJMEDS section #9
2025-06-11 18:22:53,392 - INFO - DIRECT FILTER: Processing medication #1: Aspirin/Dipyridamole (normalized: aspirin_dipyridamole)
2025-06-11 18:22:53,392 - INFO - DIRECT FILTER: Found medication aspirin_dipyridamole in database
2025-06-11 18:22:53,393 - INFO - DIRECT FILTER: Found value for aspirin_dipyridamole: Yes, code: None
2025-06-11 18:22:53,393 - INFO - DIRECT FILTER: Creating status element for aspirin_dipyridamole
2025-06-11 18:22:53,396 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,398 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,399 - INFO - DIRECT FILTER: Added status element for aspirin_dipyridamole with value Yes
2025-06-11 18:22:53,399 - INFO - DIRECT FILTER: Processing ADJMEDS section #10
2025-06-11 18:22:53,400 - INFO - DIRECT FILTER: Processing medication #1: Vorapaxar (normalized: vorapaxar)
2025-06-11 18:22:53,400 - INFO - DIRECT FILTER: Found medication vorapaxar in database
2025-06-11 18:22:53,400 - INFO - DIRECT FILTER: Found value for vorapaxar: Yes, code: None
2025-06-11 18:22:53,401 - INFO - DIRECT FILTER: Creating status element for vorapaxar
2025-06-11 18:22:53,403 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,405 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,406 - INFO - DIRECT FILTER: Added status element for vorapaxar with value Yes
2025-06-11 18:22:53,406 - INFO - DIRECT FILTER: Processing ADJMEDS section #11
2025-06-11 18:22:53,407 - INFO - DIRECT FILTER: Processing medication #1: Apixaban (normalized: apixaban)
2025-06-11 18:22:53,407 - INFO - DIRECT FILTER: Found medication apixaban in database
2025-06-11 18:22:53,408 - INFO - DIRECT FILTER: Found value for apixaban: Yes, code: None
2025-06-11 18:22:53,408 - INFO - DIRECT FILTER: Creating status element for apixaban
2025-06-11 18:22:53,410 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,412 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,412 - INFO - DIRECT FILTER: Added status element for apixaban with value Yes
2025-06-11 18:22:53,413 - INFO - DIRECT FILTER: Processing ADJMEDS section #12
2025-06-11 18:22:53,413 - INFO - DIRECT FILTER: Processing medication #1: Dabigatran (normalized: dabigatran)
2025-06-11 18:22:53,414 - INFO - DIRECT FILTER: Found medication dabigatran in database
2025-06-11 18:22:53,414 - INFO - DIRECT FILTER: Found value for dabigatran: Yes, code: None
2025-06-11 18:22:53,414 - INFO - DIRECT FILTER: Creating status element for dabigatran
2025-06-11 18:22:53,417 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,418 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,419 - INFO - DIRECT FILTER: Added status element for dabigatran with value Yes
2025-06-11 18:22:53,419 - INFO - DIRECT FILTER: Processing ADJMEDS section #13
2025-06-11 18:22:53,420 - INFO - DIRECT FILTER: Processing medication #1: Edoxaban (normalized: edoxaban)
2025-06-11 18:22:53,420 - INFO - DIRECT FILTER: Found medication edoxaban in database
2025-06-11 18:22:53,421 - INFO - DIRECT FILTER: Found value for edoxaban: Yes, code: None
2025-06-11 18:22:53,422 - INFO - DIRECT FILTER: Creating status element for edoxaban
2025-06-11 18:22:53,424 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,426 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,426 - INFO - DIRECT FILTER: Added status element for edoxaban with value Yes
2025-06-11 18:22:53,427 - INFO - DIRECT FILTER: Processing ADJMEDS section #14
2025-06-11 18:22:53,427 - INFO - DIRECT FILTER: Processing medication #1: Rivaroxaban (normalized: rivaroxaban)
2025-06-11 18:22:53,428 - INFO - DIRECT FILTER: Found medication rivaroxaban in database
2025-06-11 18:22:53,428 - INFO - DIRECT FILTER: Found value for rivaroxaban: Yes, code: None
2025-06-11 18:22:53,429 - INFO - DIRECT FILTER: Creating status element for rivaroxaban
2025-06-11 18:22:53,431 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,432 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,433 - INFO - DIRECT FILTER: Added status element for rivaroxaban with value Yes
2025-06-11 18:22:53,433 - INFO - DIRECT FILTER: Processing ADJMEDS section #15
2025-06-11 18:22:53,434 - INFO - DIRECT FILTER: Processing medication #1: Cangrelor (normalized: cangrelor)
2025-06-11 18:22:53,434 - INFO - DIRECT FILTER: Found medication cangrelor in database
2025-06-11 18:22:53,434 - INFO - DIRECT FILTER: Found value for cangrelor: Yes, code: None
2025-06-11 18:22:53,435 - INFO - DIRECT FILTER: Creating status element for cangrelor
2025-06-11 18:22:53,438 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,440 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,440 - INFO - DIRECT FILTER: Added status element for cangrelor with value Yes
2025-06-11 18:22:53,441 - INFO - DIRECT FILTER: Processing ADJMEDS section #16
2025-06-11 18:22:53,441 - INFO - DIRECT FILTER: Processing medication #1: Clopidogrel (normalized: clopidogrel)
2025-06-11 18:22:53,441 - INFO - DIRECT FILTER: Found medication clopidogrel in database
2025-06-11 18:22:53,442 - INFO - DIRECT FILTER: Found value for clopidogrel: Yes, code: None
2025-06-11 18:22:53,442 - INFO - DIRECT FILTER: Creating status element for clopidogrel
2025-06-11 18:22:53,445 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,447 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,447 - INFO - DIRECT FILTER: Added status element for clopidogrel with value Yes
2025-06-11 18:22:53,448 - INFO - DIRECT FILTER: Processing ADJMEDS section #17
2025-06-11 18:22:53,448 - INFO - DIRECT FILTER: Processing medication #1: Other P2Y12 (normalized: other_p2y12)
2025-06-11 18:22:53,448 - INFO - DIRECT FILTER: Found medication other_p2y12 in database
2025-06-11 18:22:53,449 - INFO - DIRECT FILTER: Found value for other_p2y12: Yes, code: None
2025-06-11 18:22:53,449 - INFO - DIRECT FILTER: Creating status element for other_p2y12
2025-06-11 18:22:53,451 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,452 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,453 - INFO - DIRECT FILTER: Added status element for other_p2y12 with value Yes
2025-06-11 18:22:53,454 - INFO - DIRECT FILTER: Processing ADJMEDS section #18
2025-06-11 18:22:53,454 - INFO - DIRECT FILTER: Processing medication #1: Prasugrel (normalized: prasugrel)
2025-06-11 18:22:53,455 - INFO - DIRECT FILTER: Found medication prasugrel in database
2025-06-11 18:22:53,455 - INFO - DIRECT FILTER: Found value for prasugrel: Yes, code: None
2025-06-11 18:22:53,456 - INFO - DIRECT FILTER: Creating status element for prasugrel
2025-06-11 18:22:53,458 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,459 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,460 - INFO - DIRECT FILTER: Added status element for prasugrel with value Yes
2025-06-11 18:22:53,460 - INFO - DIRECT FILTER: Processing ADJMEDS section #19
2025-06-11 18:22:53,461 - INFO - DIRECT FILTER: Processing medication #1: Ticagrelor (normalized: ticagrelor)
2025-06-11 18:22:53,461 - INFO - DIRECT FILTER: Found medication ticagrelor in database
2025-06-11 18:22:53,461 - INFO - DIRECT FILTER: Found value for ticagrelor: Yes, code: None
2025-06-11 18:22:53,462 - INFO - DIRECT FILTER: Creating status element for ticagrelor
2025-06-11 18:22:53,465 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,466 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,466 - INFO - DIRECT FILTER: Added status element for ticagrelor with value Yes
2025-06-11 18:22:53,467 - INFO - DIRECT FILTER: Processing ADJMEDS section #20
2025-06-11 18:22:53,467 - INFO - DIRECT FILTER: Processing medication #1: Ticlopidine (normalized: ticlopidine)
2025-06-11 18:22:53,467 - INFO - DIRECT FILTER: Found medication ticlopidine in database
2025-06-11 18:22:53,468 - INFO - DIRECT FILTER: Found value for ticlopidine: Yes, code: None
2025-06-11 18:22:53,468 - INFO - DIRECT FILTER: Creating status element for ticlopidine
2025-06-11 18:22:53,471 - INFO - DIRECT FILTER: Looked up code for Yes: 112000001851
2025-06-11 18:22:53,473 - INFO - DIRECT FILTER: Added value: 112000001851 - Yes
2025-06-11 18:22:53,473 - INFO - DIRECT FILTER: Added status element for ticlopidine with value Yes
2025-06-11 18:22:53,474 - INFO - DIRECT FILTER: Processing section FADJMEDS
2025-06-11 18:22:53,474 - INFO - DIRECT FILTER: Found 0 sections with code FADJMEDS
2025-06-11 18:22:53,475 - INFO - DIRECT FILTER: No values found for section FADJMEDS, removing status elements
