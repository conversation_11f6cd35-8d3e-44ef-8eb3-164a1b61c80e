#!/usr/bin/env python3

import pandas as pd
import os

def test_csv_lookup():
    """Test the CSV lookup logic"""
    
    # Load CSV files
    base_dir = os.path.abspath("app/v2/data_sources/ncdr")
    elements_file = os.path.join(base_dir, "elements.csv")
    selections_file = os.path.join(base_dir, "selections.csv")
    
    try:
        elements_df = pd.read_csv(elements_file, encoding='utf-8')
    except UnicodeDecodeError:
        elements_df = pd.read_csv(elements_file, encoding='latin1')
    
    try:
        selections_df = pd.read_csv(selections_file, encoding='utf-8')
    except UnicodeDecodeError:
        selections_df = pd.read_csv(selections_file, encoding='latin1')
    
    print(f"Loaded {len(elements_df)} elements and {len(selections_df)} selections")
    
    # Test field ID 12153 lookup
    element_12153 = elements_df[elements_df['Element Reference'] == 12153]
    if not element_12153.empty:
        element_12153_row = element_12153.iloc[0]
        element_code = str(element_12153_row['NCDR Code']).strip()
        element_code_system = str(element_12153_row['Code System']).strip()
        element_display_name = str(element_12153_row['Name']).strip()
        
        print(f"\nField ID 12153:")
        print(f"  Element code: {element_code}")
        print(f"  Code system: {element_code_system}")
        print(f"  Display name: {element_display_name}")
    else:
        print("Could not find element for field ID 12153")
    
    # Test field ID 9002 lookup
    element_9002 = elements_df[elements_df['Element Reference'] == 9002]
    if not element_9002.empty:
        element_9002_row = element_9002.iloc[0]
        occurred_code = str(element_9002_row['NCDR Code']).strip()
        occurred_code_system = str(element_9002_row['Code System']).strip()
        occurred_display_name = str(element_9002_row['Name']).strip()
        
        print(f"\nField ID 9002:")
        print(f"  Element code: {occurred_code}")
        print(f"  Code system: {occurred_code_system}")
        print(f"  Display name: {occurred_display_name}")
    else:
        print("Could not find element for field ID 9002")
    
    # Test selections lookup for field ID 12153
    event_selections = selections_df[selections_df['Element Reference'] == 12153]
    print(f"\nFound {len(event_selections)} selections for field ID 12153:")
    
    # Look for Air Embolism
    air_embolism = event_selections[
        event_selections['Selection Name'].str.contains('Air Embolism', case=False, na=False)
    ]
    if not air_embolism.empty:
        selection_row = air_embolism.iloc[0]
        selection_code = str(selection_row['Code']).strip()
        selection_code_system = str(selection_row['Code System']).strip()
        selection_display_name = str(selection_row['Selection Name']).strip()
        
        print(f"  Air Embolism:")
        print(f"    Selection code: {selection_code}")
        print(f"    Code system: {selection_code_system}")
        print(f"    Display name: {selection_display_name}")
    else:
        print("  Could not find Air Embolism selection")
    
    # Look for GI Bleeding
    gi_bleeding = event_selections[
        event_selections['Selection Name'].str.contains('GI Bleeding', case=False, na=False)
    ]
    if not gi_bleeding.empty:
        selection_row = gi_bleeding.iloc[0]
        selection_code = str(selection_row['Code']).strip()
        selection_code_system = str(selection_row['Code System']).strip()
        selection_display_name = str(selection_row['Selection Name']).strip()
        
        print(f"  GI Bleeding:")
        print(f"    Selection code: {selection_code}")
        print(f"    Code system: {selection_code_system}")
        print(f"    Display name: {selection_display_name}")
    else:
        print("  Could not find GI Bleeding selection")

if __name__ == "__main__":
    test_csv_lookup()
