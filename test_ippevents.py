#!/usr/bin/env python3

import sys
import os
import logging
import xml.etree.ElementTree as ET

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from v2.utils.ncdr_registry_utils import process_intra_post_procedure_events

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s:%(lineno)d - %(message)s')

def test_ippevents():
    """Test the process_intra_post_procedure_events function"""
    
    # Create a simple XML tree
    root = ET.Element("root")
    xml_tree = ET.ElementTree(root)
    
    # Create test event values
    event_values = {
        'IPPEVENTS': {
            'air_embolism': {
                'value': 'Yes',
                'field_id': '12153',
                'date': '2025-01-15'
            },
            'gi_bleeding': {
                'value': 'Yes', 
                'field_id': '12153',
                'date': '2025-01-16'
            }
        }
    }
    
    print("Testing process_intra_post_procedure_events function...")
    print(f"Event values: {event_values}")
    
    # Call the function
    try:
        process_intra_post_procedure_events(xml_tree, event_values)
        
        # Print the resulting XML
        print("\nGenerated XML:")
        ET.dump(root)
        
        # Check if sections were added
        sections = root.findall(".//section[@code='IPPEVENTS']")
        print(f"\nNumber of IPPEVENTS sections created: {len(sections)}")
        
        for i, section in enumerate(sections):
            print(f"\nSection {i+1}:")
            elements = section.findall("element")
            for j, element in enumerate(elements):
                print(f"  Element {j+1}: {element.get('displayName')} (code: {element.get('code')})")
                value = element.find("value")
                if value is not None:
                    print(f"    Value: {value.get('displayName')} (code: {value.get('code')})")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ippevents()
